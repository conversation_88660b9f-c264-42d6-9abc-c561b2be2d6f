import { initializeFirebase, db } from './firebase';
import { Book, BookApprovalStatus } from '@/types';

/**
 * Creates a test pending book in Firestore for testing the admin approval workflow
 * @returns Promise<string> - The ID of the created book
 */
export const createTestPendingBook = async (): Promise<string> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    console.log('Creating test pending book');

    // Create a reference to the books collection
    const booksRef = collection(db, 'books');

    // Create a test book with pending approval status
    const testBook = {
      title: 'Test Pending Book',
      author: 'Test Author',
      isbn: '1234567890123',
      genre: ['Fiction', 'Test'],
      condition: 'Good',
      description: 'This is a test book created for testing the admin approval workflow.',
      imageUrl: 'https://via.placeholder.com/150?text=Test+Book',
      perceivedValue: 5,
      price: 299,
      availability: 'For Sale & Exchange',
      ownerId: 'testuser123',
      ownerName: 'Test User',
      ownerLocation: 'Test Location',
      ownerRating: 4.5,
      distance: 2.5,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      approvalStatus: BookApprovalStatus.Pending
    };

    // Add the book to Firestore
    const docRef = await addDoc(booksRef, testBook);
    console.log('Test pending book created successfully with ID:', docRef.id);

    return docRef.id;
  } catch (error) {
    console.error('Error creating test pending book:', error);
    throw error;
  }
};

/**
 * Checks if there are any pending books in Firestore
 * @returns Promise<boolean> - True if there are pending books, false otherwise
 */
export const checkForPendingBooks = async (): Promise<boolean> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, where, getDocs, limit } = await import('firebase/firestore');

    console.log('Checking for pending books in Firestore');

    // Create a reference to the books collection
    const booksRef = collection(db, 'books');

    // Create a query to check for pending books
    const pendingBooksQuery = query(
      booksRef,
      where('approvalStatus', '==', BookApprovalStatus.Pending),
      limit(1)
    );

    // Execute the query
    const querySnapshot = await getDocs(pendingBooksQuery);

    const hasPendingBooks = !querySnapshot.empty;
    console.log(`Pending books found: ${hasPendingBooks}`);
    
    return hasPendingBooks;
  } catch (error) {
    console.error('Error checking for pending books:', error);
    // If there's an error (like missing index), we'll assume there are no pending books
    return false;
  }
};

/**
 * Ensures there is at least one pending book in the database for testing
 * @returns Promise<string> - The ID of the created book if one was created, empty string otherwise
 */
export const ensureTestPendingBook = async (): Promise<string> => {
  try {
    // Check if there are any pending books
    const hasPendingBooks = await checkForPendingBooks();

    // If there are no pending books, create a test one
    if (!hasPendingBooks) {
      console.log('No pending books found, creating a test pending book');
      return await createTestPendingBook();
    }

    console.log('Pending books already exist, no need to create a test book');
    return '';
  } catch (error) {
    console.error('Error ensuring test pending book:', error);
    return '';
  }
};
