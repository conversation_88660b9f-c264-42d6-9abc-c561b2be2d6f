/**
 * useBookSorting Hook
 * 
 * Custom hook for managing book sorting state with localStorage persistence
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  SortCriteria, 
  sortBooks, 
  getDefaultSortCriteria 
} from '@/lib/bookSortingUtils';
import { Book } from '@/types/index';

const SORT_STORAGE_KEY = 'peerbooks-sort-preference';

interface UseBookSortingReturn {
  /** Current sort criteria */
  sortCriteria: SortCriteria;
  /** Function to change sort criteria */
  setSortCriteria: (criteria: SortCriteria) => void;
  /** Function to sort books array */
  sortBooks: (books: Book[], userCommunity?: string) => Book[];
  /** Reset to default sort */
  resetSort: () => void;
}

/**
 * Custom hook for managing book sorting state
 * 
 * @returns Object containing sort state and functions
 */
export const useBookSorting = (): UseBookSortingReturn => {
  const [sortCriteria, setSortCriteriaState] = useState<SortCriteria>(getDefaultSortCriteria());

  // Load saved sort preference on mount
  useEffect(() => {
    try {
      const savedSort = localStorage.getItem(SORT_STORAGE_KEY);
      if (savedSort) {
        const parsedSort = savedSort as SortCriteria;
        // Validate that the saved sort is still valid
        const validSorts: SortCriteria[] = [
          'community-distance',
          'price-low-high',
          'price-high-low',
          'rental-low-high',
          'rental-high-low',
          'distance',
          'newest-first',
          'oldest-first'
        ];
        
        if (validSorts.includes(parsedSort)) {
          setSortCriteriaState(parsedSort);
        }
      }
    } catch (error) {
      console.warn('Failed to load sort preference from localStorage:', error);
    }
  }, []);

  // Save sort preference when it changes
  const setSortCriteria = useCallback((criteria: SortCriteria) => {
    setSortCriteriaState(criteria);
    
    try {
      localStorage.setItem(SORT_STORAGE_KEY, criteria);
    } catch (error) {
      console.warn('Failed to save sort preference to localStorage:', error);
    }
  }, []);

  // Function to sort books using current criteria
  const sortBooksWithCriteria = useCallback((books: Book[], userCommunity?: string) => {
    return sortBooks(books, sortCriteria, userCommunity);
  }, [sortCriteria]);

  // Reset to default sort
  const resetSort = useCallback(() => {
    const defaultSort = getDefaultSortCriteria();
    setSortCriteria(defaultSort);
  }, [setSortCriteria]);

  return {
    sortCriteria,
    setSortCriteria,
    sortBooks: sortBooksWithCriteria,
    resetSort
  };
};
