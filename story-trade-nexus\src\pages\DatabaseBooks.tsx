import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { getAllBooks } from '@/lib/bookService';
import { Book, BookApprovalStatus } from '@/types';
import { Skeleton } from '@/components/ui/skeleton';
import { RefreshCw, BookOpen, Check, X, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

const DatabaseBooks: React.FC = () => {
  const [books, setBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [includeUnapproved, setIncludeUnapproved] = useState(true);

  useEffect(() => {
    fetchBooks();
  }, [includeUnapproved]);

  const fetchBooks = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching all books from database...');
      
      // Get all books from Firebase (including unapproved if selected)
      const fetchedBooks = await getAllBooks(includeUnapproved);
      setBooks(fetchedBooks);
      
      console.log(`Found ${fetchedBooks.length} books in the database`);
    } catch (error) {
      console.error('Error fetching books:', error);
      setError('Failed to fetch books from the database');
      toast.error('Failed to fetch books');
    } finally {
      setLoading(false);
    }
  };

  const getApprovalStatusBadge = (status?: BookApprovalStatus) => {
    if (!status || status === BookApprovalStatus.Approved) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <Check className="w-3 h-3 mr-1" />Approved
      </span>;
    } else if (status === BookApprovalStatus.Pending) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        <AlertTriangle className="w-3 h-3 mr-1" />Pending
      </span>;
    } else {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
        <X className="w-3 h-3 mr-1" />Rejected
      </span>;
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <h1 className="text-3xl font-playfair font-bold text-navy-800 mb-2">Database Books</h1>
              <p className="text-gray-600">View all books in the database</p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="includeUnapproved"
                  checked={includeUnapproved}
                  onChange={(e) => setIncludeUnapproved(e.target.checked)}
                  className="mr-2 h-4 w-4"
                />
                <label htmlFor="includeUnapproved" className="text-sm text-gray-700">
                  Include unapproved books
                </label>
              </div>
              
              <Button
                variant="outline"
                onClick={fetchBooks}
                disabled={loading}
                className="text-sm"
              >
                {loading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </>
                )}
              </Button>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
              <p>{error}</p>
            </div>
          )}

          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-4">
                  <div className="flex flex-col md:flex-row gap-4">
                    <Skeleton className="h-32 w-32 rounded-md" />
                    <div className="flex-1">
                      <Skeleton className="h-6 w-3/4 mb-2" />
                      <Skeleton className="h-4 w-1/2 mb-4" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : books.length > 0 ? (
            <div className="space-y-4">
              {books.map((book) => (
                <div key={book.id} className="bg-white rounded-lg shadow-md p-4">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="w-32 h-32 flex-shrink-0">
                      <img
                        src={book.imageUrl || 'https://via.placeholder.com/150?text=No+Image'}
                        alt={book.title}
                        className="w-full h-full object-cover rounded-md"
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start">
                        <div>
                          <h2 className="text-xl font-medium text-navy-800">{book.title}</h2>
                          <p className="text-gray-600 mb-2">by {book.author}</p>
                        </div>
                        <div className="mb-2 sm:mb-0">
                          {getApprovalStatusBadge(book.approvalStatus)}
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 mb-2">
                        <div>
                          <span className="text-sm text-gray-500">ID:</span> {book.id}
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">Owner:</span> {book.ownerName} ({book.ownerId})
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">Genre:</span> {Array.isArray(book.genre) ? book.genre.join(', ') : book.genre}
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">Condition:</span> {book.condition}
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">Availability:</span> {book.availability}
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">Created:</span> {formatDate(book.createdAt)}
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-700 mb-3 line-clamp-2">{book.description}</p>
                      
                      <div className="flex gap-2">
                        <Link to={`/books/${book.id}`}>
                          <Button variant="outline" size="sm" className="flex items-center gap-1">
                            <BookOpen className="h-4 w-4" />
                            View Details
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-gray-50 rounded-lg p-8 text-center">
              <p className="text-lg text-gray-600 mb-2">No books found in the database</p>
              <p className="text-burgundy-500 mb-4">Try seeding the database with sample books</p>
              <Link to="/seed-books">
                <Button>Seed Database</Button>
              </Link>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default DatabaseBooks;
