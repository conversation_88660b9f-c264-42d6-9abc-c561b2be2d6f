import { db, initializeFirebase } from './firebase';
import { UserRole } from '@/types';
import { GeoCoordinates } from './geolocationUtils';

// Define User type to avoid importing from firebase/auth
type User = {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
};

export interface UserData {
  uid: string;
  email: string;
  displayName: string;
  phone?: string;
  address?: string;
  apartment?: string;
  city?: string;
  state?: string;
  pincode?: string;
  community?: string; // Community name
  gpsCoordinates?: GeoCoordinates; // GPS coordinates for location
  createdAt: any; // Firestore Timestamp
  updatedAt: any; // Firestore Timestamp
  photoURL?: string;
  wishlist?: string[]; // Book IDs
  ownedBooks?: string[]; // Book IDs
  role?: UserRole; // User role (user or admin)
}

/**
 * Creates a new user document in Firestore
 */
export const createUserDocument = async (
  user: User,
  additionalData?: {
    phone?: string;
    address?: string;
    apartment?: string;
    city?: string;
    state?: string;
    pincode?: string;
    community?: string;
    gpsCoordinates?: GeoCoordinates;
    [key: string]: any;
  }
): Promise<void> => {
  if (!user) {
    console.error('No user provided to createUserDocument');
    return;
  }

  console.log('createUserDocument called for user:', user.uid);
  console.log('Additional data received:', additionalData);

  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const {
      doc,
      getDoc,
      setDoc,
      updateDoc,
      serverTimestamp
    } = await import('firebase/firestore');

    // Reference to the user document
    const userRef = doc(db, 'users', user.uid);
    console.log('User document reference created for path:', `users/${user.uid}`);

    // Check if the user document already exists
    console.log('Checking if user document already exists...');
    const userSnapshot = await getDoc(userRef);
    console.log('User document exists:', userSnapshot.exists());

    // If the user document doesn't exist, create it
    if (!userSnapshot.exists()) {
      const { email, displayName, photoURL } = user;
      console.log('Creating new user document with data:', { email, displayName });

      const userData = {
        uid: user.uid,
        email,
        displayName: displayName || email?.split('@')[0] || 'User',
        photoURL: photoURL || null,
        phone: additionalData?.phone || '',
        address: additionalData?.address || '',
        apartment: additionalData?.apartment || '',
        city: additionalData?.city || '',
        state: additionalData?.state || '',
        pincode: additionalData?.pincode || '',
        community: additionalData?.community || '',
        gpsCoordinates: additionalData?.gpsCoordinates || null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        wishlist: [],
        ownedBooks: [],
        role: UserRole.User // Default role is regular user
      };

      console.log('User document data to be saved:', userData);

      try {
        console.log('Setting document in Firestore...');
        await setDoc(userRef, userData);
        console.log('User document created successfully');
      } catch (error) {
        console.error('Error creating user document', error);
        throw error;
      }
    } else {
      // Update the user document with the latest information
      console.log('Updating existing user document');
      try {
        const updateData = {
          updatedAt: serverTimestamp(),
          ...(additionalData || {})
        };
        console.log('Update data:', updateData);

        await updateDoc(userRef, updateData);
        console.log('User document updated successfully');
      } catch (updateError) {
        console.error('Error updating user document', updateError);
        throw updateError;
      }
    }
  } catch (error) {
    console.error('Error in createUserDocument', error);
    throw error;
  }
};

/**
 * Gets a user document from Firestore
 */
export const getUserDocument = async (uid: string): Promise<UserData | null> => {
  if (!uid) {
    console.error('No uid provided to getUserDocument');
    return null;
  }

  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc } = await import('firebase/firestore');

    console.log(`Fetching user document for uid: ${uid}`);
    const userRef = doc(db, 'users', uid);
    const userSnapshot = await getDoc(userRef);

    if (userSnapshot.exists()) {
      console.log('User document found');
      return userSnapshot.data() as UserData;
    }

    console.log('User document not found');
    return null;
  } catch (error) {
    console.error('Error getting user document', error);
    // Return null instead of throwing to prevent app crashes
    return null;
  }
};

/**
 * Updates a user document in Firestore
 */
export const updateUserDocument = async (
  uid: string,
  data: {
    displayName?: string;
    phone?: string;
    address?: string;
    apartment?: string;
    city?: string;
    state?: string;
    pincode?: string;
    community?: string;
    [key: string]: any;
  }
): Promise<void> => {
  if (!uid) {
    console.error('No uid provided to updateUserDocument');
    throw new Error('User ID is required');
  }

  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, updateDoc, serverTimestamp } = await import('firebase/firestore');

    console.log(`Updating user document for uid: ${uid}`);
    console.log('Update data:', data);

    const userRef = doc(db, 'users', uid);

    // Add updatedAt timestamp to the data
    const updateData = {
      ...data,
      updatedAt: serverTimestamp()
    };

    await updateDoc(userRef, updateData);
    console.log('User document updated successfully');
  } catch (error) {
    console.error('Error updating user document', error);
    throw error;
  }
};

/**
 * Checks if a user is an admin
 */
export const isUserAdmin = async (uid: string): Promise<boolean> => {
  try {
    console.log(`Checking if user ${uid} is an admin...`);
    const userData = await getUserDocument(uid);
    const isAdmin = userData?.role === UserRole.Admin;
    console.log(`User ${uid} admin status:`, isAdmin);
    return isAdmin;
  } catch (error) {
    console.error('Error checking if user is admin:', error);
    return false;
  }
};

/**
 * Gets all users from Firestore
 */
export const getAllUsers = async (): Promise<UserData[]> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, getDocs } = await import('firebase/firestore');

    console.log('Fetching all users...');
    const usersRef = collection(db, 'users');
    const usersSnapshot = await getDocs(usersRef);

    const users: UserData[] = [];
    usersSnapshot.forEach((doc) => {
      users.push(doc.data() as UserData);
    });

    console.log(`Found ${users.length} users`);
    return users;
  } catch (error) {
    console.error('Error getting all users:', error);
    return [];
  }
};

/**
 * Sets a user as an admin
 */
export const setUserAsAdmin = async (uid: string): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, updateDoc } = await import('firebase/firestore');

    console.log(`Setting user ${uid} as admin...`);
    const userRef = doc(db, 'users', uid);

    await updateDoc(userRef, {
      role: UserRole.Admin
    });

    console.log(`User ${uid} set as admin successfully`);
  } catch (error) {
    console.error('Error setting user as admin:', error);
    throw error;
  }
};

/**
 * Removes admin role from a user
 */
export const removeAdminRole = async (uid: string): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, updateDoc } = await import('firebase/firestore');

    console.log(`Removing admin role from user ${uid}...`);
    const userRef = doc(db, 'users', uid);

    await updateDoc(userRef, {
      role: UserRole.User
    });

    console.log(`Admin role removed from user ${uid} successfully`);
  } catch (error) {
    console.error('Error removing admin role:', error);
    throw error;
  }
};

/**
 * Creates a new user as an admin
 * This function is used by administrators to create new user accounts
 */
export const createUserAsAdmin = async (
  email: string,
  password: string,
  userData: {
    displayName: string;
    phone?: string;
    address?: string;
    apartment?: string;
    city?: string;
    state?: string;
    pincode?: string;
    community?: string;
    role: UserRole;
  }
): Promise<{success: boolean, message: string, uid?: string}> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firebase auth and Firestore functions
    const {
      createUserWithEmailAndPassword,
      updateProfile,
      sendEmailVerification
    } = await import('firebase/auth');
    const {
      doc,
      setDoc,
      serverTimestamp
    } = await import('firebase/firestore');

    console.log('Creating new user as admin with email:', email);
    console.log('User data:', userData);

    // Create user with email and password
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    console.log('User created in Firebase Auth:', user.uid);

    // Update the user's profile with the display name
    if (userData.displayName) {
      await updateProfile(user, { displayName: userData.displayName });
      console.log('User profile updated with display name:', userData.displayName);
    }

    // Send email verification
    try {
      await sendEmailVerification(user);
      console.log('Verification email sent to:', email);
    } catch (verificationError) {
      console.error('Error sending verification email:', verificationError);
      // Continue without throwing - user is still created
    }

    // Create user document in Firestore
    const userRef = doc(db, 'users', user.uid);
    const userDocData = {
      uid: user.uid,
      email,
      displayName: userData.displayName || email.split('@')[0],
      phone: userData.phone || '',
      address: userData.address || '',
      apartment: userData.apartment || '',
      city: userData.city || '',
      state: userData.state || '',
      pincode: userData.pincode || '',
      community: userData.community || '',
      photoURL: null,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      wishlist: [],
      ownedBooks: [],
      role: userData.role || UserRole.User
    };

    await setDoc(userRef, userDocData);
    console.log('User document created in Firestore:', user.uid);

    return {
      success: true,
      message: `User ${email} created successfully`,
      uid: user.uid
    };
  } catch (error) {
    console.error('Error creating user as admin:', error);

    // Extract error message
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;

      // Handle Firebase auth errors
      if (errorMessage.includes('email-already-in-use')) {
        errorMessage = 'This email is already in use. Please use a different email.';
      } else if (errorMessage.includes('weak-password')) {
        errorMessage = 'The password is too weak. Please use a stronger password.';
      } else if (errorMessage.includes('invalid-email')) {
        errorMessage = 'The email address is not valid.';
      }
    }

    return {
      success: false,
      message: errorMessage
    };
  }
};

/**
 * Deletes a user from Firestore
 * This function removes the user document from Firestore database
 * Note: This does not delete the user from Firebase Authentication
 */
export const deleteUser = async (uid: string): Promise<{success: boolean, message: string}> => {
  if (!uid) {
    console.error('No uid provided to deleteUser');
    return {
      success: false,
      message: 'User ID is required'
    };
  }

  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, deleteDoc, getDoc } = await import('firebase/firestore');

    console.log(`Deleting user document for uid: ${uid}`);

    // First check if the user exists
    const userRef = doc(db, 'users', uid);
    const userSnapshot = await getDoc(userRef);

    if (!userSnapshot.exists()) {
      console.log(`User with ID ${uid} not found`);
      return {
        success: false,
        message: 'User not found'
      };
    }

    // Get user data for logging
    const userData = userSnapshot.data();
    console.log(`Deleting user: ${userData.displayName || userData.email} (${uid})`);

    // Delete the user document
    await deleteDoc(userRef);
    console.log(`User ${uid} deleted successfully from Firestore`);

    return {
      success: true,
      message: `User ${userData.displayName || userData.email} deleted successfully`
    };
  } catch (error) {
    console.error('Error deleting user:', error);
    return {
      success: false,
      message: `Failed to delete user: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

export const setupDirectAdmin = async (): Promise<{success: boolean, message: string}> => {
  try {
    console.log('Starting admin setup process...');

    // Initialize Firebase
    await initializeFirebase();
    console.log('Firebase initialized successfully');

    // Dynamically import Firestore functions
    const { collection, query, where, getDocs, doc, setDoc, getDoc } = await import('firebase/firestore');
    console.log('Firebase Firestore functions imported successfully');

    const adminEmail = '<EMAIL>';
    console.log(`Setting up admin user with email: ${adminEmail}`);

    // Find the user with the provided email
    const usersRef = collection(db, 'users');
    console.log('Users collection reference created');

    const q = query(usersRef, where('email', '==', adminEmail));
    console.log('Query created for email:', adminEmail);

    const querySnapshot = await getDocs(q);
    console.log('Query executed, found documents:', querySnapshot.size);

    let userId = '';
    let userData: any = null;

    if (querySnapshot.empty) {
      console.log(`No user found with email: ${adminEmail} in query results`);
      console.log('Checking if user exists in authentication but not in Firestore...');

      // Try to create a user document if it doesn't exist
      // This is a fallback in case the user exists in Authentication but not in Firestore
      try {
        // Create a user document with a UID based on the email
        userId = adminEmail.replace(/[^a-zA-Z0-9]/g, '');
        console.log('Generated userId:', userId);

        // Check if the document already exists
        const userDocRef = doc(db, 'users', userId);
        const userDocSnap = await getDoc(userDocRef);

        if (userDocSnap.exists()) {
          console.log('User document exists with generated ID');
          userData = userDocSnap.data();
        } else {
          console.log('Creating new user document with generated ID');

          // Create a new user document
          userData = {
            uid: userId,
            email: adminEmail,
            displayName: 'Admin User',
            role: UserRole.User, // Will be updated to Admin later
            createdAt: new Date().toISOString()
          };

          await setDoc(userDocRef, userData);
          console.log('New user document created successfully');
        }
      } catch (createError) {
        console.error('Error creating user document:', createError);
        return {
          success: false,
          message: `Failed to create user document: ${createError instanceof Error ? createError.message : 'Unknown error'}`
        };
      }
    } else {
      // Get the user document from query results
      const userDoc = querySnapshot.docs[0];
      userId = userDoc.id;
      userData = userDoc.data();
      console.log(`Found user in query results: ${userData.displayName || userData.email} (${userId})`);
    }

    // Check if user is already an admin
    if (userData.role === UserRole.Admin) {
      console.log('User is already an admin.');
      return { success: true, message: 'User is already an admin.' };
    }

    // Set the user as admin
    try {
      const userRef = doc(db, 'users', userId);
      console.log('Setting user role to Admin for userId:', userId);

      // Use setDoc with merge option to ensure it works even if some fields are missing
      await setDoc(userRef, {
        role: UserRole.Admin
      }, { merge: true });

      console.log(`Successfully set ${adminEmail} as an admin!`);
      return { success: true, message: `Successfully set ${adminEmail} as an admin!` };
    } catch (updateError) {
      console.error('Error updating user role:', updateError);
      return {
        success: false,
        message: `Failed to update user role: ${updateError instanceof Error ? updateError.message : 'Unknown error'}`
      };
    }
  } catch (error) {
    console.error('Error in setupDirectAdmin:', error);
    return {
      success: false,
      message: `Error in setupDirectAdmin: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};