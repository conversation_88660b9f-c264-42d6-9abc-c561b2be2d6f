#!/usr/bin/env node

/**
 * Firebase Setup Helper Script
 * 
 * This script helps you set up Firebase authentication for the WebP migration.
 * It provides step-by-step instructions and validates your setup.
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔥 Firebase Authentication Setup Helper\n');

async function checkExistingSetup() {
  console.log('🔍 Checking existing setup...\n');
  
  // Check for environment variable
  if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    console.log('✅ Found GOOGLE_APPLICATION_CREDENTIALS environment variable');
    console.log(`   Path: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}`);
    
    try {
      await fs.access(process.env.GOOGLE_APPLICATION_CREDENTIALS);
      console.log('✅ Service account file exists and is accessible');
      return true;
    } catch (error) {
      console.log('❌ Service account file not found or not accessible');
      console.log(`   Error: ${error.message}`);
    }
  }
  
  // Check for .env file
  const envPath = path.join(__dirname, '../.env');
  try {
    const envContent = await fs.readFile(envPath, 'utf8');
    if (envContent.includes('FIREBASE_PRIVATE_KEY') && envContent.includes('FIREBASE_CLIENT_EMAIL')) {
      console.log('✅ Found Firebase credentials in .env file');
      return true;
    }
  } catch (error) {
    console.log('ℹ️  No .env file found');
  }
  
  // Check for common service account file names
  const commonNames = [
    'service-account.json',
    'firebase-adminsdk.json',
    'book-share-98f6a-firebase-adminsdk.json'
  ];
  
  for (const name of commonNames) {
    const filePath = path.join(__dirname, '..', name);
    try {
      await fs.access(filePath);
      console.log(`✅ Found potential service account file: ${name}`);
      console.log(`   You can set: GOOGLE_APPLICATION_CREDENTIALS="${filePath}"`);
      return false; // Found file but not configured
    } catch (error) {
      // File doesn't exist, continue checking
    }
  }
  
  console.log('❌ No Firebase authentication setup found');
  return false;
}

function printSetupInstructions() {
  console.log('\n📋 SETUP INSTRUCTIONS\n');
  
  console.log('🔗 Step 1: Get Service Account Key');
  console.log('1. Go to: https://console.firebase.google.com/');
  console.log('2. Select project: book-share-98f6a');
  console.log('3. Click the gear icon (⚙️) → Project Settings');
  console.log('4. Go to the "Service Accounts" tab');
  console.log('5. Click "Generate new private key"');
  console.log('6. Download the JSON file\n');
  
  console.log('🔧 Step 2: Configure Authentication (Choose ONE method)\n');
  
  console.log('METHOD A: Environment Variable (Recommended)');
  console.log('Save the JSON file and set the environment variable:');
  console.log('');
  console.log('Windows (PowerShell):');
  console.log('$env:GOOGLE_APPLICATION_CREDENTIALS="C:\\path\\to\\your\\service-account.json"');
  console.log('');
  console.log('Windows (Command Prompt):');
  console.log('set GOOGLE_APPLICATION_CREDENTIALS=C:\\path\\to\\your\\service-account.json');
  console.log('');
  console.log('macOS/Linux:');
  console.log('export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account.json"');
  console.log('');
  
  console.log('METHOD B: .env File');
  console.log('1. Create a .env file in the story-trade-nexus directory');
  console.log('2. Add this line to the .env file:');
  console.log('GOOGLE_APPLICATION_CREDENTIALS=./path/to/your/service-account.json');
  console.log('');
  
  console.log('METHOD C: Environment Variables (Advanced)');
  console.log('Extract values from the JSON file and set these environment variables:');
  console.log('FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n"');
  console.log('FIREBASE_CLIENT_EMAIL="<EMAIL>"');
  console.log('FIREBASE_PRIVATE_KEY_ID="your-private-key-id"');
  console.log('FIREBASE_CLIENT_ID="your-client-id"');
  console.log('');
}

function printSecurityNotes() {
  console.log('⚠️  SECURITY NOTES\n');
  console.log('🔒 NEVER commit the service account JSON file to git');
  console.log('🔒 Keep the private key secure and confidential');
  console.log('🔒 The .gitignore file has been updated to prevent accidental commits');
  console.log('🔒 Only share credentials through secure channels');
  console.log('');
}

function printNextSteps() {
  console.log('🚀 NEXT STEPS\n');
  console.log('1. Complete the setup using one of the methods above');
  console.log('2. Test your configuration:');
  console.log('   npm run test:firebase');
  console.log('');
  console.log('3. If the test passes, run the migration:');
  console.log('   npm run migrate:webp');
  console.log('');
  console.log('4. Monitor the progress and check the migration report');
  console.log('');
}

async function createEnvTemplate() {
  const envPath = path.join(__dirname, '../.env');
  const envExamplePath = path.join(__dirname, '.env.example');
  
  try {
    // Check if .env already exists
    await fs.access(envPath);
    console.log('ℹ️  .env file already exists');
  } catch (error) {
    // .env doesn't exist, create from template
    try {
      const template = await fs.readFile(envExamplePath, 'utf8');
      await fs.writeFile(envPath, template);
      console.log('✅ Created .env file from template');
      console.log('   Edit the .env file to add your Firebase credentials');
    } catch (templateError) {
      console.log('ℹ️  Could not create .env file from template');
    }
  }
}

async function main() {
  try {
    const isSetup = await checkExistingSetup();
    
    if (isSetup) {
      console.log('\n🎉 Firebase authentication appears to be configured!');
      console.log('\n🧪 Test your configuration:');
      console.log('npm run test:firebase');
      console.log('\n🚀 If the test passes, you can run the migration:');
      console.log('npm run migrate:webp');
    } else {
      printSetupInstructions();
      await createEnvTemplate();
      printSecurityNotes();
      printNextSteps();
    }
    
  } catch (error) {
    console.error('❌ Setup helper failed:', error.message);
    process.exit(1);
  }
}

main();
