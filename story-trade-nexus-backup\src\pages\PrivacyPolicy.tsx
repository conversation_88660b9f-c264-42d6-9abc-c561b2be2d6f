import React from 'react';
import { Link } from 'react-router-dom';
import MainLayout from '@/components/layouts/MainLayout';

const PrivacyPolicy = () => {
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12 max-w-4xl">
        <div className="bg-white rounded-lg shadow-md p-8">
          <h1 className="text-3xl font-bold text-navy-800 font-playfair mb-6">Privacy Policy</h1>
          <p className="text-gray-600 mb-4">Effective Date: 23-05-2025</p>

          <div className="prose prose-burgundy max-w-none">
            <p className="mb-6">
              PeerBooks ("we", "our", or "us") values your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our application and related services that utilize Facebook Login.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">1. Information We Collect</h2>
            <p>When you log in using Facebook, we may collect:</p>
            <ul className="list-disc pl-6 mb-4">
              <li>Your public profile (name, profile picture)</li>
              <li>Your email address (if permitted by you)</li>
            </ul>
            <p>We do not collect or store your Facebook password or sensitive personal data.</p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">2. How We Use Your Information</h2>
            <p>We use the collected information to:</p>
            <ul className="list-disc pl-6 mb-4">
              <li>Authenticate and log you into our platform</li>
              <li>Personalize your user experience</li>
              <li>Communicate important updates or changes</li>
            </ul>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">3. Sharing of Your Information</h2>
            <p>
              We do not sell, rent, or share your data with third parties. Your data is used solely to provide services via PeerBooks.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">4. Data Retention</h2>
            <p>
              We retain your data only for as long as necessary to fulfill the purpose it was collected for, or as required by law.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">5. Data Security</h2>
            <p>
              We implement industry-standard security practices to protect your data. However, no method of transmission over the internet is completely secure.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">6. Your Rights</h2>
            <p>You may request to:</p>
            <ul className="list-disc pl-6 mb-4">
              <li>Access the data we have about you</li>
              <li>Delete your data</li>
              <li>Withdraw consent for future data use</li>
            </ul>
            <p>To do so, please visit our <Link to="/data-deletion" className="text-burgundy-500 hover:underline">Data Deletion</Link> page or contact us using the information provided in the "Contact Us" section.</p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">7. Changes to This Policy</h2>
            <p>
              We may update this Privacy Policy periodically. Continued use of the app signifies acceptance of any changes.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">8. Contact Us</h2>
            <p>
              If you have any questions about this Privacy Policy, please contact us at: <a href="mailto:<EMAIL>" className="text-burgundy-500 hover:underline"><EMAIL></a>
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">9. User Data Deletion Instructions</h2>
            <p>
              If you wish to delete your account and all associated personal data collected via Facebook and Google Login, please follow the instructions on our <Link to="/data-deletion" className="text-burgundy-500 hover:underline">Data Deletion</Link> page.
            </p>
            <p className="mt-2">
              In short, send an email to <a href="mailto:<EMAIL>" className="text-burgundy-500 hover:underline"><EMAIL></a> with the subject line "Delete My PeerBooks App Data" and include your name and the email associated with your account. Your data will be deleted within 48 hours, and you will receive a confirmation email once the deletion is complete.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">10. Additional Information for Social Login Users</h2>
            <p>
              When you use social login features (Google or Facebook), we only access the information you have explicitly allowed us to access through your privacy settings with those services. We do not request or store additional permissions beyond what is necessary for authentication and basic profile information.
            </p>
            <p>
              You can revoke our access to your social media accounts at any time through your social media account settings.
            </p>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default PrivacyPolicy;
