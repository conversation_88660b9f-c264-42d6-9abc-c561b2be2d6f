
import React from 'react';
import { FileText, Plus, Edit, Trash2, Eye } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import AdminLayout from '@/components/layout/AdminLayout';

const AdminBlogManager: React.FC = () => {
  return (
    <AdminLayout title="Blog Manager" description="Create and manage blog posts and content">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-navy-800 mb-2">Blog Manager</h1>
          <p className="text-gray-600">Create and manage blog posts and content</p>
        </div>
      </div>

      {/* Coming Soon Content */}
      <div className="bg-white rounded-lg shadow-md p-8">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-burgundy-100 mb-4">
            <FileText className="h-8 w-8 text-burgundy-600" />
          </div>
          
          <h2 className="text-xl font-bold text-navy-800 mb-2">Blog Manager - Coming Soon</h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            The blog management system is currently under development. Soon you'll be able to create, 
            edit, and manage blog posts directly from this interface.
          </p>

          {/* Feature Preview */}
          <div className="bg-gray-50 rounded-lg p-6 mb-6">
            <h3 className="text-lg font-semibold text-navy-800 mb-4">Planned Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <Plus className="h-5 w-5 text-burgundy-600 mt-0.5" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Create Blog Posts</h4>
                  <p className="text-sm text-gray-600">Rich text editor for creating engaging blog content</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <Edit className="h-5 w-5 text-burgundy-600 mt-0.5" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Edit & Update</h4>
                  <p className="text-sm text-gray-600">Modify existing posts with version control</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <Eye className="h-5 w-5 text-burgundy-600 mt-0.5" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Preview & Publish</h4>
                  <p className="text-sm text-gray-600">Preview posts before publishing to users</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <Trash2 className="h-5 w-5 text-burgundy-600 mt-0.5" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Content Management</h4>
                  <p className="text-sm text-gray-600">Organize, categorize, and archive blog posts</p>
                </div>
              </div>
            </div>
          </div>

          {/* Status Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <p className="text-blue-800 text-sm">
              <strong>Development Status:</strong> This feature is planned for a future release. 
              Stay tuned for updates on the blog management functionality.
            </p>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminBlogManager;

