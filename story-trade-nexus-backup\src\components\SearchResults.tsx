import React from 'react';
import { Book } from '@/types/index';
import BookCard from './BookCard';
import { Button } from './ui/button-variants';
import { Link } from 'react-router-dom';
import { Search, BookPlus, X } from 'lucide-react';

interface SearchResultsProps {
  results: Book[];
  query: string;
  loading: boolean;
  onClearSearch: () => void;
}

const SearchResults: React.FC<SearchResultsProps> = ({ 
  results, 
  query, 
  loading, 
  onClearSearch 
}) => {
  if (loading) {
    return (
      <div className="py-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-playfair font-bold text-navy-800">
              Searching for "{query}"...
            </h2>
            <Button variant="ghost" size="sm" onClick={onClearSearch}>
              <X className="h-4 w-4 mr-2" />
              Clear Search
            </Button>
          </div>
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-burgundy-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (results.length === 0) {
    return (
      <div className="py-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-playfair font-bold text-navy-800">
              Search Results for "{query}"
            </h2>
            <Button variant="ghost" size="sm" onClick={onClearSearch}>
              <X className="h-4 w-4 mr-2" />
              Clear Search
            </Button>
          </div>
          <div className="bg-beige-50 rounded-lg p-8 text-center">
            <Search className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-medium text-gray-700 mb-2">No Books Found</h3>
            <p className="text-gray-600 mb-6">
              We couldn't find any books matching "{query}". Try a different search term or browse all books.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button onClick={onClearSearch}>
                Try Another Search
              </Button>
              <Link to="/browse">
                <Button variant="outline">
                  Browse All Books
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-8">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-playfair font-bold text-navy-800">
              Search Results for "{query}"
            </h2>
            <p className="text-gray-600">
              Found {results.length} {results.length === 1 ? 'book' : 'books'} matching your search
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClearSearch}>
            <X className="h-4 w-4 mr-2" />
            Clear Search
          </Button>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {results.map((book) => (
            <BookCard key={book.id} book={book} />
          ))}
        </div>
        
        <div className="mt-8 text-center">
          <p className="text-gray-600 mb-4">
            Want to see more books?
          </p>
          <Link to="/browse">
            <Button variant="outline">
              Browse All Books
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SearchResults;
