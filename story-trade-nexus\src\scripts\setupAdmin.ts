/**
 * This script sets up the first admin user in the system.
 * Run this script once to create an admin user.
 *
 * Usage:
 * 1. Update the email address below to the email of the user you want to make an admin
 * 2. Run the script with: npx ts-node src/scripts/setupAdmin.ts
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, query, where, getDocs, doc, updateDoc } from 'firebase/firestore';
import { UserRole } from '../types';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDQYgJtZVCCVrGvNub-YGNbfUGXRF9hRYs",
  authDomain: "book-share-98f6a.firebaseapp.com",
  projectId: "book-share-98f6a",
  storageBucket: "book-share-98f6a.appspot.com",
  messagingSenderId: "1098127383693",
  appId: "1:1098127383693:web:e9c0a0a7a6d3a0d0a0a0a0"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Email of the user to make admin
const adminEmail = '<EMAIL>'; // This is already set to the requested email

async function setupAdmin() {
  try {
    console.log(`Setting up admin user with email: ${adminEmail}`);

    // Find the user with the provided email
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('email', '==', adminEmail));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.error(`No user found with email: ${adminEmail}`);
      console.log('Make sure the user has registered and verified their email first.');
      return;
    }

    // Get the user document
    const userDoc = querySnapshot.docs[0];
    const userId = userDoc.id;
    const userData = userDoc.data();

    console.log(`Found user: ${userData.displayName || userData.email} (${userId})`);

    // Check if user is already an admin
    if (userData.role === UserRole.Admin) {
      console.log('User is already an admin.');
      return;
    }

    // Set the user as admin
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      role: UserRole.Admin
    });

    console.log(`Successfully set ${adminEmail} as an admin!`);
  } catch (error) {
    console.error('Error setting up admin:', error);
  }
}

// Run the script
setupAdmin();
