
import React, { useState } from 'react';
import { Search, Filter } from 'lucide-react';
import { Button } from './ui/button-variants';

interface SearchBarProps {
  onSearch?: (query: string) => void;
  onFilter?: () => void;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearch, onFilter, className }) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(searchQuery);
    }
  };

  return (
    <div className={`w-full ${className}`}>
      <form onSubmit={handleSubmit} className="flex">
        <div className="relative flex-grow">
          <input
            type="text"
            placeholder="Search by title, author, or ISBN..."
            className="w-full pl-10 pr-4 py-3 rounded-l-lg border-y border-l focus:ring-1 focus:ring-burgundy-500 focus:border-burgundy-500 outline-none"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Search className="absolute left-3 top-3.5 h-4 w-4 text-gray-400" />
        </div>
        <Button type="submit" className="rounded-l-none">
          Search
        </Button>
        {onFilter && (
          <Button 
            type="button" 
            variant="outline" 
            className="ml-2 border border-gray-300 hover:bg-gray-50"
            onClick={onFilter}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        )}
      </form>
    </div>
  );
};

export default SearchBar;
