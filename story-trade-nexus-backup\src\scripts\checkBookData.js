// Script to check book data in Firestore
import { initializeApp } from 'firebase/app';
import { getFirestore, doc, getDoc } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDXQnxRLuSWnhj9QkO-UGOGbxJz0Gqfzz0",
  authDomain: "book-share-98f6a.firebaseapp.com",
  projectId: "book-share-98f6a",
  storageBucket: "book-share-98f6a.appspot.com",
  messagingSenderId: "1051281275473",
  appId: "1:1051281275473:web:c6e3a7d3d1a1de2e3a8f9c"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Function to check book data
async function checkBookData(bookId) {
  try {
    console.log(`Checking book with ID: ${bookId}`);
    
    // Get the book document from Firestore
    const bookRef = doc(db, 'books', bookId);
    const bookSnapshot = await getDoc(bookRef);
    
    if (!bookSnapshot.exists()) {
      console.log(`No book found with ID: ${bookId}`);
      return;
    }
    
    // Get the book data
    const data = bookSnapshot.data();
    
    // Log the book data
    console.log('Book data:', data);
    
    // Check security deposit fields
    console.log('Security deposit required:', data.securityDepositRequired);
    console.log('Security deposit amount:', data.securityDepositAmount);
    console.log('Security deposit types:', {
      required: typeof data.securityDepositRequired,
      amount: typeof data.securityDepositAmount
    });
    
    // Check if the book is the one we're looking for
    if (data.title.includes('Mystery Of The Missing Cat')) {
      console.log('FOUND TARGET BOOK: Mystery Of The Missing Cat');
      console.log('Security deposit details:', {
        required: data.securityDepositRequired,
        amount: data.securityDepositAmount
      });
    }
  } catch (error) {
    console.error('Error checking book data:', error);
  }
}

// Check the specific book
checkBookData('mystery-cat-123');
