import React from 'react';
import { Check, X, RefreshCw } from 'lucide-react';
import { BookAvailability } from '@/types';

interface AvailabilityIndicatorProps {
  availability: string;
  className?: string;
}

const AvailabilityIndicator: React.FC<AvailabilityIndicatorProps> = ({ 
  availability,
  className = ''
}) => {
  // Check if the book is available for exchange
  const isExchangeAvailable = 
    availability === 'For Exchange' || 
    availability === 'For Rent & Exchange' || 
    availability === 'For Sale & Exchange' || 
    availability === 'For Rent, Sale & Exchange';

  // Check if the book is available for rent
  const isRentAvailable = 
    availability === 'For Rent' || 
    availability === 'For Rent & Sale' || 
    availability === 'For Rent & Exchange' || 
    availability === 'For Rent, Sale & Exchange';

  // Check if the book is available for sale
  const isSaleAvailable = 
    availability === 'For Sale' || 
    availability === 'For Rent & Sale' || 
    availability === 'For Sale & Exchange' || 
    availability === 'For Rent, Sale & Exchange';

  return (
    <div className={`rounded-lg border p-4 ${className}`}>
      <h3 className="font-medium text-navy-800 mb-3 text-lg">Availability Status</h3>
      
      <div className="space-y-3">
        {/* Exchange availability */}
        <div className={`flex items-center p-3 rounded-md ${isExchangeAvailable ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'}`}>
          {isExchangeAvailable ? (
            <>
              <Check className="h-5 w-5 mr-3 text-green-600" />
              <div>
                <p className="font-medium text-green-800">Available for Exchange</p>
                <p className="text-sm text-green-700 mt-1">
                  This book can be exchanged with other books
                </p>
              </div>
            </>
          ) : (
            <>
              <X className="h-5 w-5 mr-3 text-gray-500" />
              <div>
                <p className="font-medium text-gray-700">Not Available for Exchange</p>
                <p className="text-sm text-gray-500 mt-1">
                  This book is not available for exchange
                </p>
              </div>
            </>
          )}
        </div>

        {/* Rent availability - only show if available */}
        {isRentAvailable && (
          <div className="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-md">
            <RefreshCw className="h-5 w-5 mr-3 text-blue-600" />
            <div>
              <p className="font-medium text-blue-800">Available for Rent</p>
              <p className="text-sm text-blue-700 mt-1">
                This book can be rented
              </p>
            </div>
          </div>
        )}

        {/* Sale availability - only show if available */}
        {isSaleAvailable && (
          <div className="flex items-center p-3 bg-amber-50 border border-amber-200 rounded-md">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-3 text-amber-600">
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
            </svg>
            <div>
              <p className="font-medium text-amber-800">Available for Purchase</p>
              <p className="text-sm text-amber-700 mt-1">
                This book is available for sale
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AvailabilityIndicator;
