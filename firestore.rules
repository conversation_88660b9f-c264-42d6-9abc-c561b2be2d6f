rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Existing book-related rules (preserving current functionality)
    match /books/{book} {
      allow read, write: if false; // Books collection remains read-only for now
    }
    
    // Blog posts collection
    match /blogPosts/{postId} {
      // Allow read access for all users
      allow read: if true;
      
      // Admin-only write access (using same credentials as book management)
      allow create, update, delete: if request.auth != null && request.auth.uid in get(/databases/$(database)/documents/admins).data.uids;
      
      // Allow update of specific fields for publishing
      allow update: if request.auth != null && request.auth.uid in get(/databases/$(database)/documents/admins).data.uids &&
                    request.resource.data.diff(request.resource.data).affectedKeys().hasOnly(['published', 'content', 'title', 'slug', 'tags', 'author', 'coverImageUrl', 'createdAt', 'updatedAt', 'draft']);
    }
    
    // Admins collection to store admin UIDs
    match /admins/{admin} {
      allow read: if request.auth != null;
      allow write: if false;
    }
  }
}
