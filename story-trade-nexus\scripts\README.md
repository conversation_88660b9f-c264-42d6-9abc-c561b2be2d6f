# WebP Migration Script for PeerBooks

This directory contains scripts and utilities for migrating existing book images from their current formats (JPEG, PNG, etc.) to WebP format for better performance and smaller file sizes.

## Overview

The WebP migration process includes:
- **Batch processing** of all images in Firebase Storage
- **Automatic conversion** to WebP format with optimization
- **Database updates** to reflect new file extensions
- **Backup creation** for rollback capability
- **Comprehensive logging** and reporting
- **Error handling** and recovery

## Prerequisites

### 1. Dependencies
Make sure you have installed the required dependencies:
```bash
npm install sharp firebase-admin
```

### 2. Firebase Authentication
You need to set up Firebase Admin SDK authentication. Choose one of the following methods:

#### Option A: Service Account Key File (Recommended)
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project (`book-share-98f6a`)
3. Go to Project Settings > Service Accounts
4. Click "Generate new private key" and download the JSON file
5. Set the environment variable:
```bash
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"
```

#### Option B: Environment Variables
Set these environment variables from your service account JSON:
```bash
export FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
export FIREBASE_CLIENT_EMAIL="<EMAIL>"
export FIREBASE_PRIVATE_KEY_ID="your-private-key-id"
export FIREBASE_CLIENT_ID="your-client-id"
```

#### Option C: Google Cloud Environment
If running in Google Cloud (Cloud Functions, Cloud Run, etc.), the script will use default credentials automatically.

## Usage

### Running the Migration

1. **Test Firebase Connection** (Optional but recommended):
```bash
node scripts/firebase-config.js
```

2. **Run the Migration**:
```bash
node scripts/webp-migration.js migrate
```

### Migration Process

The script will:
1. **Initialize** Firebase Admin SDK and test connections
2. **Scan** Firebase Storage for all image files in `book-images/` directory
3. **Process** images in batches (configurable batch size)
4. **Convert** each image to WebP format with optimization
5. **Upload** converted images back to Firebase Storage
6. **Update** database references in Firestore
7. **Clean up** original files after successful conversion
8. **Generate** a comprehensive migration report

### Configuration

You can modify the configuration in `webp-migration.js`:

```javascript
const CONFIG = {
  // Image processing settings
  maxWidth: 1200,
  webpQuality: 85,
  webpEffort: 6,

  // Batch processing settings
  batchSize: 10,
  delayBetweenBatches: 1000, // ms

  // Backup settings
  backupDir: path.join(__dirname, '../backups/webp-migration'),
  createBackups: true,

  // Logging
  logLevel: 'info', // 'debug', 'info', 'warn', 'error'
};
```

## Output and Logs

### Log Files
- **Migration logs**: `logs/webp-migration.log`
- **Migration report**: `backups/webp-migration/migration-report.json`

### Backup Files
Original images are backed up to: `backups/webp-migration/book-images/`

### Migration Report
The script generates a detailed report including:
- Processing statistics (success/failure rates)
- Storage savings (original vs WebP file sizes)
- Compression ratios
- Database update counts
- Error details for failed conversions

Example report:
```json
{
  "migration": {
    "startTime": "2025-01-15T10:00:00.000Z",
    "endTime": "2025-01-15T10:15:30.000Z",
    "duration": "930s"
  },
  "results": {
    "totalFiles": 150,
    "successful": 148,
    "failed": 2,
    "successRate": "98.67%"
  },
  "storage": {
    "originalSize": "45.2 MB",
    "webpSize": "18.7 MB",
    "spaceSaved": "26.5 MB",
    "compressionRatio": "58.63%"
  },
  "database": {
    "recordsUpdated": 75
  }
}
```

## Rollback (Emergency)

If you need to rollback the migration:

```bash
node scripts/webp-migration.js rollback backups/webp-migration/migration-report.json
```

**Note**: Rollback functionality is currently limited. For manual rollback:
1. Restore original files from the backup directory
2. Update database references back to original extensions
3. Delete WebP files from Firebase Storage

## Safety Features

### Backup Strategy
- **Automatic backups** of all original files before conversion
- **Metadata preservation** including original file information
- **Incremental processing** to handle large datasets safely

### Error Handling
- **Graceful failure handling** - continues processing other files if one fails
- **Detailed error logging** for troubleshooting
- **Validation checks** before processing
- **Connection testing** before starting migration

### Monitoring
- **Real-time progress tracking** with batch-level reporting
- **Comprehensive logging** at multiple levels (debug, info, warn, error)
- **Storage usage tracking** and compression statistics

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify Firebase service account credentials
   - Check project ID and permissions
   - Ensure service account has Storage Admin and Firestore Admin roles

2. **Memory Issues**
   - Reduce batch size in configuration
   - Increase delay between batches
   - Monitor system memory usage

3. **Network Timeouts**
   - Check internet connection stability
   - Increase timeout values if needed
   - Consider running during off-peak hours

4. **Storage Quota**
   - Verify Firebase Storage quota limits
   - Monitor storage usage during migration
   - Consider temporary quota increases if needed

### Getting Help

If you encounter issues:
1. Check the log files for detailed error messages
2. Verify Firebase permissions and quotas
3. Test with a small batch first
4. Contact the development team with log files and error details

## Post-Migration

After successful migration:

1. **Verify** that all images are displaying correctly in the application
2. **Test** image upload functionality with new WebP processing
3. **Monitor** application performance improvements
4. **Clean up** backup files after confirming everything works correctly
5. **Update** any hardcoded image URLs or references if needed

## Future Uploads

The updated `storageService.ts` now automatically:
- **Converts** new uploads to WebP format
- **Optimizes** images according to the same standards
- **Maintains** backward compatibility
- **Provides** fallback for browsers without WebP support

New images uploaded through the application will automatically use WebP format, ensuring consistent performance benefits going forward.
