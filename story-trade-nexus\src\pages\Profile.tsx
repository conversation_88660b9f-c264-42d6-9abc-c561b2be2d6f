import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  BookOpen,
  Heart,
  Edit,
  Save,
  X,
  Home,
  Building,
  Map
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'sonner';
import { useAuth } from '@/lib/AuthContext';
import MainLayout from '@/components/layouts/MainLayout';
import { getBooksByOwner } from '@/lib/bookService';
import { Book } from '@/types';
import { updateUserDocument } from '@/lib/userService';

const Profile = () => {
  const { currentUser, userData, refreshUserData } = useAuth();
  const [userBooks, setUserBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({
    displayName: '',
    phone: '',
    address: '',
    apartment: '',
    city: '',
    state: '',
    pincode: ''
  });

  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);
        
        // Fetch user's books
        const books = await getBooksByOwner(currentUser.uid);
        setUserBooks(books);
        
        // Initialize form data with current user data
        if (userData) {
          setFormData({
            displayName: userData.displayName || '',
            phone: userData.phone || '',
            address: userData.address || '',
            apartment: userData.apartment || '',
            city: userData.city || '',
            state: userData.state || '',
            pincode: userData.pincode || ''
          });
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast.error('Failed to load profile data');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [currentUser, userData]);

  // Format date to readable format
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentUser) return;
    
    try {
      await updateUserDocument(currentUser.uid, formData);
      await refreshUserData();
      toast.success('Profile updated successfully');
      setEditMode(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    }
  };

  // Cancel edit mode
  const handleCancel = () => {
    // Reset form data to current user data
    if (userData) {
      setFormData({
        displayName: userData.displayName || '',
        phone: userData.phone || '',
        address: userData.address || '',
        apartment: userData.apartment || '',
        city: userData.city || '',
        state: userData.state || '',
        pincode: userData.pincode || ''
      });
    }
    setEditMode(false);
  };

  // Get user initials for avatar fallback
  const getInitials = () => {
    if (userData?.displayName) {
      return userData.displayName
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .substring(0, 2);
    }
    return currentUser?.email?.substring(0, 2).toUpperCase() || "U";
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex flex-col md:flex-row gap-8">
              <div className="md:w-1/3">
                <Skeleton className="h-40 w-40 rounded-full mx-auto" />
                <div className="mt-4 space-y-2">
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
              <div className="md:w-2/3 space-y-6">
                <Skeleton className="h-8 w-1/2" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!currentUser || !userData) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <h1 className="text-2xl font-bold text-navy-800 mb-4">
              User Not Found
            </h1>
            <p className="text-gray-600 mb-6">
              Please sign in to view your profile.
            </p>
            <Link to="/signin">
              <Button>
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-navy-800">My Profile</h1>
            {!editMode ? (
              <Button onClick={() => setEditMode(true)} className="flex items-center gap-2">
                <Edit className="h-4 w-4" />
                Edit Profile
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleCancel} className="flex items-center gap-2">
                  <X className="h-4 w-4" />
                  Cancel
                </Button>
                <Button onClick={handleSubmit} className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Save Changes
                </Button>
              </div>
            )}
          </div>

          <div className="flex flex-col md:flex-row gap-8">
            {/* Profile Sidebar */}
            <div className="md:w-1/3">
              <div className="flex flex-col items-center">
                <Avatar className="h-32 w-32">
                  <AvatarImage
                    src={userData.photoURL || ""}
                    alt={userData.displayName || "User"}
                  />
                  <AvatarFallback className="text-3xl bg-burgundy-100 text-burgundy-700">
                    {getInitials()}
                  </AvatarFallback>
                </Avatar>
                <h2 className="mt-4 text-xl font-semibold">
                  {editMode ? (
                    <Input
                      name="displayName"
                      value={formData.displayName}
                      onChange={handleInputChange}
                      className="text-center"
                    />
                  ) : (
                    userData.displayName
                  )}
                </h2>
                <p className="text-gray-600">{userData.email}</p>
                <p className="text-sm text-gray-500 mt-1">
                  Member since {formatDate(userData.createdAt)}
                </p>
              </div>

              <div className="mt-6 bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-navy-800 mb-3">Account Statistics</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <BookOpen className="h-4 w-4 text-burgundy-500 mr-2" />
                      <span>Books Added</span>
                    </div>
                    <span className="font-medium">{userBooks.length}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Heart className="h-4 w-4 text-burgundy-500 mr-2" />
                      <span>Wishlist Items</span>
                    </div>
                    <span className="font-medium">{userData.wishlist?.length || 0}</span>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <Link to="/dashboard">
                  <Button variant="outline" className="w-full">
                    Go to Dashboard
                  </Button>
                </Link>
              </div>
            </div>

            {/* Profile Details */}
            <div className="md:w-2/3">
              <div className="bg-gray-50 rounded-lg p-5 mb-6">
                <h3 className="font-medium text-navy-800 mb-4 flex items-center">
                  <User className="h-5 w-5 mr-2 text-burgundy-500" />
                  Personal Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <div className="flex items-center mt-1">
                      <Mail className="h-4 w-4 text-gray-400 mr-2" />
                      <p>{userData.email}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <div className="flex items-center mt-1">
                      <Phone className="h-4 w-4 text-gray-400 mr-2" />
                      {editMode ? (
                        <Input
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          placeholder="Enter phone number"
                        />
                      ) : (
                        <p>{userData.phone || 'Not provided'}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-5">
                <h3 className="font-medium text-navy-800 mb-4 flex items-center">
                  <MapPin className="h-5 w-5 mr-2 text-burgundy-500" />
                  Address Information
                </h3>
                {editMode ? (
                  <form className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <label className="text-sm text-gray-500">Address</label>
                      <Input
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        placeholder="Enter your address"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <label className="text-sm text-gray-500">City</label>
                      <Input
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        placeholder="Enter city"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <label className="text-sm text-gray-500">State</label>
                      <Input
                        name="state"
                        value={formData.state}
                        onChange={handleInputChange}
                        placeholder="Enter state"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <label className="text-sm text-gray-500">Pincode</label>
                      <Input
                        name="pincode"
                        value={formData.pincode}
                        onChange={handleInputChange}
                        placeholder="Enter pincode"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <label className="text-sm text-gray-500">Apartment/Building</label>
                      <Input
                        name="apartment"
                        value={formData.apartment}
                        onChange={handleInputChange}
                        placeholder="Enter apartment or building name"
                        className="mt-1"
                      />
                    </div>
                  </form>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <p className="text-sm text-gray-500">Address</p>
                      <div className="flex items-start mt-1">
                        <Home className="h-4 w-4 text-gray-400 mr-2 mt-1" />
                        <p>{userData.address || 'Not provided'}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">City</p>
                      <p className="mt-1">{userData.city || 'Not provided'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">State</p>
                      <p className="mt-1">{userData.state || 'Not provided'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Pincode</p>
                      <p className="mt-1">{userData.pincode || 'Not provided'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Apartment/Building</p>
                      <div className="flex items-start mt-1">
                        <Building className="h-4 w-4 text-gray-400 mr-2 mt-1" />
                        <p>{userData.apartment || 'Not provided'}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Profile;
