# Book Sharing App - Utility Scripts

This directory contains utility scripts for managing and interacting with the Book Sharing application's database.

## List All Books Script

The `list-all-books.js` script connects to Firebase Firestore and retrieves all books along with their owner information. It displays the results in a well-formatted table in the terminal.

### Features

- Connects to Firebase Firestore using the project's configuration
- Retrieves all books from the 'books' collection
- Displays book details including title, author, owner name, owner ID, and creation date
- Sorts books by creation date (newest first)
- Shows approval status and whether location information is available
- Provides summary statistics about books in the database

### How to Run

1. Open a terminal/command prompt
2. Navigate to the project directory:
   ```
   cd path/to/story-trade-nexus
   ```
3. Run the script:
   ```
   node list-all-books.js
   ```

### Requirements

- Node.js version 14 or higher
- Firebase project with Firestore database
- Internet connection to access Firebase services

### Troubleshooting

If you encounter any issues:

1. Make sure you have the correct Firebase configuration in the script
2. Check your internet connection
3. Verify that your Firebase project is properly set up
4. Ensure you have the necessary permissions to access the Firestore database

## Other Utility Scripts

### Update Book by ID

The `update-book-direct.js` script updates a specific book with location information:

```
node update-book-direct.js <book-id>
```

### Find Books by Owner

The `find-books-by-owner.js` script finds all books owned by a specific user:

```
node find-books-by-owner.js <owner-id>
```

### Update Harry Book

The `update-harry-direct.js` script updates all books with "Harry" in the title:

```
node update-harry-direct.js
```

## Common Issues and Solutions

### Module Not Found Error

If you see an error like `Error [ERR_MODULE_NOT_FOUND]`, make sure:

1. You're using the correct file path
2. The module you're trying to import exists
3. You're using the correct import syntax for ES modules

### Firebase Connection Issues

If you have trouble connecting to Firebase:

1. Check your internet connection
2. Verify your Firebase configuration (apiKey, projectId, etc.)
3. Make sure your Firebase project is active
4. Check if you have the necessary permissions to access the database

### Script Execution Errors

If the script fails to execute:

1. Make sure you're using a compatible Node.js version
2. Check for syntax errors in the script
3. Verify that all required dependencies are installed
