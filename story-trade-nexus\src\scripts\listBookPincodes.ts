/**
 * <PERSON><PERSON><PERSON> to list all books with their pincode and location information
 */

import { initializeFirebase, db } from '../lib/firebase';

// Self-executing async function
(async () => {
  console.log('Listing all books with pincode and location information...');
  
  try {
    // Initialize Firebase
    await initializeFirebase();
    
    // Dynamically import Firestore functions
    const { collection, getDocs } = await import('firebase/firestore');
    
    // Fetch all books from Firestore
    const booksRef = collection(db, 'books');
    const booksSnapshot = await getDocs(booksRef);
    
    if (booksSnapshot.empty) {
      console.log('No books found in the database');
      process.exit(0);
      return;
    }
    
    console.log(`Found ${booksSnapshot.size} books in the database\n`);
    
    // Track statistics
    let booksWithPincode = 0;
    let booksWithLocation = 0;
    let booksWithCoordinates = 0;
    let booksNeedingUpdate = 0;
    
    // Create a table of books with their pincode and location information
    console.log('ID | Title | Pincode | Location | Has Coordinates');
    console.log('-'.repeat(100));
    
    booksSnapshot.forEach(bookDoc => {
      const data = bookDoc.data();
      const id = bookDoc.id;
      const title = data.title || 'Unknown Title';
      const pincode = data.ownerPincode || 'N/A';
      const location = data.ownerLocation || 'N/A';
      const hasCoordinates = data.ownerCoordinates ? 'Yes' : 'No';
      
      // Truncate title if too long
      const truncatedTitle = title.length > 30 ? title.substring(0, 27) + '...' : title;
      
      console.log(`${id} | ${truncatedTitle} | ${pincode} | ${location} | ${hasCoordinates}`);
      
      // Update statistics
      if (pincode !== 'N/A') booksWithPincode++;
      if (location !== 'N/A' && location !== 'Unknown Location') booksWithLocation++;
      if (hasCoordinates === 'Yes') booksWithCoordinates++;
      
      // Check if book needs update
      if (pincode !== 'N/A' && 
          (location === 'N/A' || 
           location === 'Unknown Location' || 
           hasCoordinates === 'No')) {
        booksNeedingUpdate++;
      }
    });
    
    // Print statistics
    console.log('\nStatistics:');
    console.log(`Total books: ${booksSnapshot.size}`);
    console.log(`Books with pincode: ${booksWithPincode} (${Math.round(booksWithPincode / booksSnapshot.size * 100)}%)`);
    console.log(`Books with location: ${booksWithLocation} (${Math.round(booksWithLocation / booksSnapshot.size * 100)}%)`);
    console.log(`Books with coordinates: ${booksWithCoordinates} (${Math.round(booksWithCoordinates / booksSnapshot.size * 100)}%)`);
    console.log(`Books needing update: ${booksNeedingUpdate} (${Math.round(booksNeedingUpdate / booksSnapshot.size * 100)}%)`);
    
  } catch (error) {
    console.error('Error listing books:', error);
  }
  
  // Force exit after completion
  process.exit(0);
})();
