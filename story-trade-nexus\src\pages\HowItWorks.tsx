import React from 'react';
import { Link } from 'react-router-dom';
import MainLayout from '@/components/layouts/MainLayout';
import {
  DollarSign,
  Clock,
  RefreshCw,
  MapPin,
  Shield,
  Users,
  Handshake,
  BookOpen,
  ArrowRight,
  Calendar,
  CheckCircle,
  AlertCircle,
  Percent
} from 'lucide-react';

const HowItWorksPage: React.FC = () => {
  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-beige-500 to-beige-100 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-playfair font-bold text-navy-800 mb-6">
              How <span className="text-burgundy-500">PeerBooks</span> Works
            </h1>
            <p className="text-lg text-gray-700 mb-8">
              PeerBooks connects book lovers directly with each other to buy, rent, or exchange books.
              Learn how our platform works and start sharing your literary treasures today.
            </p>
          </div>
        </div>
      </section>

      {/* Sale Section */}
      <section className="py-16 bg-white" id="sale">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center mb-12">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-12">
              <div className="bg-burgundy-500 text-white inline-block rounded-full p-3 mb-4">
                <DollarSign className="h-8 w-8" />
              </div>
              <h2 className="text-3xl font-playfair font-bold text-navy-800 mb-4">Selling Books</h2>
              <p className="text-gray-700 mb-6">
                Sell your books directly to interested readers in your community. Set your own prices and arrange meetups on your terms.
              </p>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="bg-beige-200 rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">List your book:</span> Add details, photos, condition, and set your selling price.</p>
                </div>
                <div className="flex items-start">
                  <div className="bg-beige-200 rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">Connect with buyers:</span> Interested readers will contact you through the platform.</p>
                </div>
                <div className="flex items-start">
                  <div className="bg-beige-200 rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">Arrange meetup:</span> Coordinate a safe, public location to complete the transaction.</p>
                </div>
                <div className="flex items-start">
                  <div className="bg-beige-200 rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">Complete the sale:</span> Exchange the book for payment and mark the transaction as complete.</p>
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="bg-beige-100 rounded-lg p-8 shadow-md">
                <h3 className="text-xl font-playfair font-bold text-navy-800 mb-4">Seller Tips</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Take clear photos of your book from multiple angles</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Be honest about the condition (marks, highlights, wear)</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Research market prices to set a competitive rate</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Respond promptly to buyer inquiries</span>
                  </li>
                  <li className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="font-medium">Remember:</span> <span>You set the price - PeerBooks doesn't take any commission</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Rent Section */}
      <section className="py-16 bg-beige-100" id="rent">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center mb-12">
            <div className="md:w-1/2 md:order-2 mb-8 md:mb-0 md:pl-12">
              <div className="bg-burgundy-500 text-white inline-block rounded-full p-3 mb-4">
                <Clock className="h-8 w-8" />
              </div>
              <h2 className="text-3xl font-playfair font-bold text-navy-800 mb-4">Renting Books</h2>
              <p className="text-gray-700 mb-6">
                Rent out your books temporarily to earn recurring income while still keeping your collection. Set rental periods and rates that work for you.
              </p>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="bg-white rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">List for rent:</span> Specify rental period options (weekly/monthly) and rates.</p>
                </div>
                <div className="flex items-start">
                  <div className="bg-white rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">Collect deposit:</span> We recommend collecting a refundable security deposit (30-50% of book value).</p>
                </div>
                <div className="flex items-start">
                  <div className="bg-white rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">Track return date:</span> Agree on a clear return date and method with the renter.</p>
                </div>
                <div className="flex items-start">
                  <div className="bg-white rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">Verify condition:</span> Check the book's condition upon return before refunding the deposit.</p>
                </div>
              </div>
            </div>
            <div className="md:w-1/2 md:order-1">
              <div className="bg-white rounded-lg p-8 shadow-md">
                <h3 className="text-xl font-playfair font-bold text-navy-800 mb-4">Rental Guidelines</h3>
                <div className="space-y-6">
                  <div>
                    <h4 className="font-medium text-navy-700 flex items-center mb-2">
                      <Calendar className="h-5 w-5 mr-2 text-burgundy-500" />
                      Setting Rental Periods
                    </h4>
                    <p className="text-gray-700 text-sm">
                      Offer flexible rental periods based on the book type. For novels, 2-4 weeks is typical. For textbooks, consider semester-long rentals.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-navy-700 flex items-center mb-2">
                      <Percent className="h-5 w-5 mr-2 text-burgundy-500" />
                      Security Deposits
                    </h4>
                    <p className="text-gray-700 text-sm">
                      Always collect a refundable security deposit of 30-50% of the book's value to protect against damage or non-return.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-navy-700 flex items-center mb-2">
                      <DollarSign className="h-5 w-5 mr-2 text-burgundy-500" />
                      Pricing Recommendations
                    </h4>
                    <p className="text-gray-700 text-sm">
                      Set weekly rates at 15-20% of the book's value. Monthly rates should offer a discount compared to weekly rates.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Exchange Section */}
      <section className="py-16 bg-white" id="exchange">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center mb-12">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-12">
              <div className="bg-burgundy-500 text-white inline-block rounded-full p-3 mb-4">
                <RefreshCw className="h-8 w-8" />
              </div>
              <h2 className="text-3xl font-playfair font-bold text-navy-800 mb-4">Exchanging Books</h2>
              <p className="text-gray-700 mb-6">
                Swap books with other readers to refresh your collection without spending money. Our value-matching system helps ensure fair exchanges.
              </p>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="bg-beige-200 rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">Find matching books:</span> Browse books with similar perceived value to yours.</p>
                </div>
                <div className="flex items-start">
                  <div className="bg-beige-200 rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">Propose an exchange:</span> Contact the owner and suggest a swap.</p>
                </div>
                <div className="flex items-start">
                  <div className="bg-beige-200 rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">Handle value differences:</span> For unequal exchanges, the owner of the lower-value book may offer additional compensation.</p>
                </div>
                <div className="flex items-start">
                  <div className="bg-beige-200 rounded-full p-1 mr-3 mt-1">
                    <ArrowRight className="h-4 w-4 text-burgundy-500" />
                  </div>
                  <p className="text-gray-700"><span className="font-medium">Meet and exchange:</span> Arrange a meetup to swap books and complete the transaction.</p>
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="bg-beige-100 rounded-lg p-8 shadow-md">
                <h3 className="text-xl font-playfair font-bold text-navy-800 mb-4">Value Matching System</h3>
                <p className="text-gray-700 mb-4">
                  Our platform helps you find fair exchanges through our value-matching system:
                </p>
                <div className="bg-white rounded-lg p-4 mb-6">
                  <div className="flex justify-between items-center mb-4 border-b pb-3">
                    <div className="flex items-center">
                      <BookOpen className="h-5 w-5 text-burgundy-500 mr-2" />
                      <span className="font-medium">Your Book</span>
                    </div>
                    <span className="bg-navy-100 text-navy-800 px-2 py-1 rounded text-sm">Value: 8/10</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <BookOpen className="h-5 w-5 text-burgundy-500 mr-2" />
                      <span className="font-medium">Their Book</span>
                    </div>
                    <span className="bg-navy-100 text-navy-800 px-2 py-1 rounded text-sm">Value: 6/10</span>
                  </div>
                  <div className="mt-4 pt-3 border-t text-sm">
                    <p className="text-gray-700">
                      <span className="font-medium">Value Difference:</span> 2 points
                    </p>
                    <p className="text-gray-700">
                      <span className="font-medium">Recommended Compensation:</span> 20-30% of the book's market value
                    </p>
                  </div>
                </div>
                <div className="text-sm text-gray-700">
                  <p className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>For unequal exchanges, we recommend the owner of the lower-value book compensate 20-30% of the difference in market value.</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Policies Section */}
      <section className="py-16 bg-navy-500 text-white" id="policies">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="bg-navy-400 text-white inline-block rounded-full p-3 mb-4">
              <Shield className="h-8 w-8" />
            </div>
            <h2 className="text-3xl font-playfair font-bold mb-4">Platform Policies</h2>
            <p className="text-beige-100 max-w-2xl mx-auto">
              PeerBooks is a connection platform that brings book lovers together. Here's what you should know about how we operate:
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-navy-600 rounded-lg p-6">
              <div className="bg-navy-400 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-playfair font-bold mb-3">Peer-to-Peer Transactions</h3>
              <p className="text-beige-100 text-sm">
                All transactions occur directly between users. PeerBooks serves only as a connection platform to help you find and communicate with other book lovers.
              </p>
            </div>

            <div className="bg-navy-600 rounded-lg p-6">
              <div className="bg-navy-400 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-playfair font-bold mb-3">No Transaction Fees</h3>
              <p className="text-beige-100 text-sm">
                PeerBooks does not charge any transaction fees or commissions. The full payment goes directly from buyer to seller, or between exchange partners.
              </p>
            </div>

            <div className="bg-navy-600 rounded-lg p-6">
              <div className="bg-navy-400 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                <Handshake className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-playfair font-bold mb-3">No Payment Processing</h3>
              <p className="text-beige-100 text-sm">
                PeerBooks does not handle payments or provide payment processing. Users are responsible for arranging their own payment methods for transactions.
              </p>
            </div>

            <div className="bg-navy-600 rounded-lg p-6">
              <div className="bg-navy-400 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                <AlertCircle className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-playfair font-bold mb-3">Dispute Resolution</h3>
              <p className="text-beige-100 text-sm">
                PeerBooks does not mediate disputes between users. We encourage users to communicate clearly, verify book conditions in person, and agree on terms before completing transactions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Location Services Section */}
      <section className="py-16 bg-beige-100" id="location">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-12">
              <div className="bg-burgundy-500 text-white inline-block rounded-full p-3 mb-4">
                <MapPin className="h-8 w-8" />
              </div>
              <h2 className="text-3xl font-playfair font-bold text-navy-800 mb-4">Location Services</h2>
              <p className="text-gray-700 mb-6">
                Location services are essential to the PeerBooks experience, helping you find books near you and connect with local readers.
              </p>
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-medium text-navy-700 mb-2">Why Location Matters</h3>
                  <p className="text-gray-700">
                    Enabling location services allows you to:
                  </p>
                  <ul className="mt-2 space-y-2">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Find books within your preferred distance radius</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>See exact distance between you and book owners</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Sort search results by proximity</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-xl font-medium text-navy-700 mb-2">How It Works</h3>
                  <p className="text-gray-700">
                    Our platform uses GPS coordinates to calculate the distance between users. When you enable location services, we store your coordinates securely and use them only to:
                  </p>
                  <ul className="mt-2 space-y-2">
                    <li className="flex items-start">
                      <ArrowRight className="h-5 w-5 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Calculate distances between you and book listings</span>
                    </li>
                    <li className="flex items-start">
                      <ArrowRight className="h-5 w-5 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Display your general location area (not your exact address) to other users</span>
                    </li>
                    <li className="flex items-start">
                      <ArrowRight className="h-5 w-5 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Provide location-based search and filtering</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="bg-white rounded-lg p-8 shadow-md">
                <h3 className="text-xl font-playfair font-bold text-navy-800 mb-4">Privacy Considerations</h3>
                <div className="space-y-4">
                  <p className="text-gray-700">
                    We take your privacy seriously. Here's how we protect your location data:
                  </p>
                  <div className="bg-beige-50 rounded-lg p-4">
                    <h4 className="font-medium text-navy-700 mb-2">What We Share</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span>Distance between users (e.g., "3.2 km away")</span>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span>General neighborhood or area (e.g., "South Delhi")</span>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span>City and pincode information</span>
                      </li>
                    </ul>
                  </div>
                  <div className="bg-beige-50 rounded-lg p-4">
                    <h4 className="font-medium text-navy-700 mb-2">What We Don't Share</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <AlertCircle className="h-4 w-4 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span>Your exact GPS coordinates</span>
                      </li>
                      <li className="flex items-start">
                        <AlertCircle className="h-4 w-4 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span>Your precise home address</span>
                      </li>
                      <li className="flex items-start">
                        <AlertCircle className="h-4 w-4 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span>Location history or movement patterns</span>
                      </li>
                    </ul>
                  </div>
                  <p className="text-sm text-gray-600 italic">
                    You can update your location at any time from your profile settings. We recommend refreshing your location periodically if you move or travel frequently.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-burgundy-500">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-playfair font-bold text-white mb-4">Ready to Start Sharing Books?</h2>
          <p className="text-beige-100 mb-8 max-w-2xl mx-auto">
            Join our community of book lovers and start buying, renting, or exchanging books with readers near you.
          </p>
          <div className="flex flex-col md:flex-row justify-center space-y-4 md:space-y-0 md:space-x-4">
            <Link to="/join" className="bg-white text-burgundy-500 hover:bg-beige-100 font-medium px-6 py-3 rounded-md shadow-md transition-colors">
              Sign Up Now
            </Link>
            <Link to="/browse" className="bg-transparent border border-white text-white hover:bg-burgundy-600 font-medium px-6 py-3 rounded-md shadow-md transition-colors">
              Browse Books
            </Link>
          </div>
        </div>
      </section>
    </MainLayout>
  );
};

export default HowItWorksPage;