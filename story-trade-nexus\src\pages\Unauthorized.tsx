import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ShieldAlert } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';

const Unauthorized: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow flex items-center justify-center">
        <div className="max-w-md w-full mx-auto p-8 bg-white rounded-lg shadow-lg text-center">
          <ShieldAlert className="h-16 w-16 text-burgundy-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-navy-800 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-6">
            You don't have permission to access this page. This area is restricted to administrators only.
          </p>
          <div className="flex flex-col space-y-2">
            <Link to="/">
              <Button className="w-full">Return to Home</Button>
            </Link>
            <Link to="/browse">
              <Button variant="outline" className="w-full">Browse Books</Button>
            </Link>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Unauthorized;
