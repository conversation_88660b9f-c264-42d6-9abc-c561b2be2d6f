{"name": "firebase-community-query-tool", "version": "1.0.0", "description": "Command-line tool to query Firebase Firestore for community names based on pincode", "type": "module", "main": "queryCommunities.js", "scripts": {"query": "node queryCommunities.js", "help": "node queryCommunities.js --help"}, "keywords": ["firebase", "firestore", "community", "pincode", "query", "cli"], "dependencies": {"firebase": "^11.8.0"}, "engines": {"node": ">=14.0.0"}, "author": "PeerBooks Team", "license": "MIT"}