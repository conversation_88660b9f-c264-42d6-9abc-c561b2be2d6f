/**
 * Date Utilities
 * 
 * Optimized date-fns imports to reduce bundle size
 * Import only specific functions instead of the entire library
 */

// Import specific date-fns functions to enable tree shaking
import { format as formatDate } from 'date-fns/format';
import { parseISO } from 'date-fns/parseISO';
import { isValid } from 'date-fns/isValid';
import { addDays } from 'date-fns/addDays';
import { subDays } from 'date-fns/subDays';
import { startOfDay } from 'date-fns/startOfDay';
import { endOfDay } from 'date-fns/endOfDay';
import { differenceInDays } from 'date-fns/differenceInDays';
import { isBefore } from 'date-fns/isBefore';
import { isAfter } from 'date-fns/isAfter';
import { isSameDay } from 'date-fns/isSameDay';

// Common date formats used in the application
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  DISPLAY_WITH_TIME: 'MMM dd, yyyy HH:mm',
  ISO: 'yyyy-MM-dd',
  TIMESTAMP: 'yyyy-MM-dd HH:mm:ss',
  INDIAN: 'dd/MM/yyyy',
  INDIAN_WITH_TIME: 'dd/MM/yyyy HH:mm'
} as const;

// Optimized date formatting functions
export const format = (date: Date | string | number, formatStr: string = DATE_FORMATS.DISPLAY): string => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
    if (!isValid(dateObj)) {
      console.warn('Invalid date provided to format function:', date);
      return 'Invalid Date';
    }
    return formatDate(dateObj, formatStr);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

// Common date operations
export const formatDisplayDate = (date: Date | string | number): string => 
  format(date, DATE_FORMATS.DISPLAY);

export const formatDisplayDateTime = (date: Date | string | number): string => 
  format(date, DATE_FORMATS.DISPLAY_WITH_TIME);

export const formatIndianDate = (date: Date | string | number): string => 
  format(date, DATE_FORMATS.INDIAN);

export const formatIndianDateTime = (date: Date | string | number): string => 
  format(date, DATE_FORMATS.INDIAN_WITH_TIME);

// Date validation
export const isValidDate = (date: any): boolean => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
    return isValid(dateObj);
  } catch {
    return false;
  }
};

// Date calculations
export const addDaysToDate = (date: Date | string, days: number): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return addDays(dateObj, days);
};

export const subtractDaysFromDate = (date: Date | string, days: number): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return subDays(dateObj, days);
};

export const getDaysDifference = (date1: Date | string, date2: Date | string): number => {
  const dateObj1 = typeof date1 === 'string' ? parseISO(date1) : date1;
  const dateObj2 = typeof date2 === 'string' ? parseISO(date2) : date2;
  return differenceInDays(dateObj1, dateObj2);
};

// Date comparisons
export const isDateBefore = (date1: Date | string, date2: Date | string): boolean => {
  const dateObj1 = typeof date1 === 'string' ? parseISO(date1) : date1;
  const dateObj2 = typeof date2 === 'string' ? parseISO(date2) : date2;
  return isBefore(dateObj1, dateObj2);
};

export const isDateAfter = (date1: Date | string, date2: Date | string): boolean => {
  const dateObj1 = typeof date1 === 'string' ? parseISO(date1) : date1;
  const dateObj2 = typeof date2 === 'string' ? parseISO(date2) : date2;
  return isAfter(dateObj1, dateObj2);
};

export const isSameDayAs = (date1: Date | string, date2: Date | string): boolean => {
  const dateObj1 = typeof date1 === 'string' ? parseISO(date1) : date1;
  const dateObj2 = typeof date2 === 'string' ? parseISO(date2) : date2;
  return isSameDay(dateObj1, dateObj2);
};

// Date range utilities
export const getStartOfDay = (date: Date | string): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return startOfDay(dateObj);
};

export const getEndOfDay = (date: Date | string): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return endOfDay(dateObj);
};

// Utility for converting Firestore timestamps
export const formatFirestoreDate = (timestamp: any): string => {
  try {
    if (!timestamp) return 'No date';
    
    // Handle Firestore Timestamp
    if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      return format(timestamp.toDate(), DATE_FORMATS.DISPLAY);
    }
    
    // Handle regular Date or string
    return format(timestamp, DATE_FORMATS.DISPLAY);
  } catch (error) {
    console.error('Error formatting Firestore date:', error);
    return 'Invalid Date';
  }
};

// Export all date-fns functions that might be needed elsewhere
export {
  parseISO,
  isValid,
  addDays,
  subDays,
  startOfDay,
  endOfDay,
  differenceInDays,
  isBefore,
  isAfter,
  isSameDay
};
