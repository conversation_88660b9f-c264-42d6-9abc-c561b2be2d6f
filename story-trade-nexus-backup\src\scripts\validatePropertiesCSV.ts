/**
 * Validation script for properties CSV files
 *
 * This script validates CSV file format and data integrity
 * before running the main update script. Supports configurable CSV file paths.
 *
 * Usage:
 * - Default: npm run validate:csv
 * - Custom file: npm run validate:csv -- --csv-file=my-data.csv
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */

import fs from 'fs';
import { parse } from 'csv-parse';
import path from 'path';

// TypeScript interfaces
interface CSVRow {
  communityname: string;
  address: string;
  pincode: string;
  latitude: string;
  longitude: string;
}

interface ValidationSummary {
  totalRows: number;
  validRows: number;
  invalidRows: number;
  errors: string[];
  warnings: string[];
  pincodeRange: {
    min: string;
    max: string;
    unique: number;
  };
  coordinateBounds: {
    latMin: number;
    latMax: number;
    lngMin: number;
    lngMax: number;
  };
}

// Command line arguments
const args = process.argv.slice(2);
const csvFileArg = args.find(arg => arg.startsWith('--csv-file=') || arg.startsWith('--input='));

// Configuration
const DEFAULT_CSV_FILENAME = 'hyderabad_properties_500010-500040.csv';
let csvFileName = DEFAULT_CSV_FILENAME;
if (csvFileArg) {
  csvFileName = csvFileArg.split('=')[1];
}
const CSV_FILE_PATH = path.resolve(process.cwd(), csvFileName);
const REQUIRED_FIELDS = ['communityname', 'address', 'pincode', 'latitude', 'longitude'];

// Logger utility
const logger = {
  info: (message: string) => console.log(`ℹ️  ${message}`),
  success: (message: string) => console.log(`✅ ${message}`),
  error: (message: string) => console.error(`❌ ${message}`),
  warning: (message: string) => console.warn(`⚠️  ${message}`)
};

/**
 * Validates a single CSV row
 */
function validateRow(row: CSVRow, rowIndex: number): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check required fields
  for (const field of REQUIRED_FIELDS) {
    if (!row[field as keyof CSVRow] || row[field as keyof CSVRow].trim() === '') {
      errors.push(`Row ${rowIndex}: Missing required field '${field}'`);
    }
  }

  // Validate pincode
  if (row.pincode) {
    const pincode = row.pincode.trim();
    if (!/^\d{6}$/.test(pincode)) {
      errors.push(`Row ${rowIndex}: Invalid pincode format '${pincode}' (must be 6 digits)`);
    }
  }

  // Validate coordinates
  if (row.latitude) {
    const lat = parseFloat(row.latitude);
    if (isNaN(lat) || lat < -90 || lat > 90) {
      errors.push(`Row ${rowIndex}: Invalid latitude '${row.latitude}' (must be between -90 and 90)`);
    }
  }

  if (row.longitude) {
    const lng = parseFloat(row.longitude);
    if (isNaN(lng) || lng < -180 || lng > 180) {
      errors.push(`Row ${rowIndex}: Invalid longitude '${row.longitude}' (must be between -180 and 180)`);
    }
  }

  // Validate community name length
  if (row.communityname && row.communityname.trim().length > 200) {
    errors.push(`Row ${rowIndex}: Community name too long (${row.communityname.trim().length} chars, max 200)`);
  }

  // Validate address length
  if (row.address && row.address.trim().length > 500) {
    errors.push(`Row ${rowIndex}: Address too long (${row.address.trim().length} chars, max 500)`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Reads and validates the CSV file
 */
async function validateCSVFile(): Promise<ValidationSummary> {
  return new Promise((resolve, reject) => {
    logger.info('Starting CSV validation...');

    // Check if file exists
    if (!fs.existsSync(CSV_FILE_PATH)) {
      reject(new Error(`CSV file not found: ${CSV_FILE_PATH}`));
      return;
    }

    logger.info(`Reading CSV file: ${CSV_FILE_PATH}`);

    const records: CSVRow[] = [];
    const errors: string[] = [];
    const warnings: string[] = [];
    const pincodes = new Set<string>();
    const coordinates: { lat: number; lng: number }[] = [];

    const fileStream = fs.createReadStream(CSV_FILE_PATH);
    const parser = parse({
      columns: true,
      skip_empty_lines: true,
      trim: true
    });

    fileStream
      .pipe(parser)
      .on('data', (row: CSVRow) => {
        records.push(row);
      })
      .on('end', () => {
        logger.success(`Read ${records.length} records from CSV`);

        // Validate each record
        let validRows = 0;
        let invalidRows = 0;

        records.forEach((row, index) => {
          const validation = validateRow(row, index + 1);

          if (validation.isValid) {
            validRows++;

            // Collect statistics for valid rows
            if (row.pincode) {
              pincodes.add(row.pincode.trim());
            }

            const lat = parseFloat(row.latitude);
            const lng = parseFloat(row.longitude);
            if (!isNaN(lat) && !isNaN(lng)) {
              coordinates.push({ lat, lng });
            }
          } else {
            invalidRows++;
            errors.push(...validation.errors);
          }
        });

        // Check for duplicates
        const communityNames = records.map(r => r.communityname?.trim().toLowerCase()).filter(Boolean);
        const duplicateNames = communityNames.filter((name, index) => communityNames.indexOf(name) !== index);
        if (duplicateNames.length > 0) {
          warnings.push(`Found ${duplicateNames.length} potential duplicate community names`);
        }

        // Calculate coordinate bounds
        let coordinateBounds = {
          latMin: 0, latMax: 0, lngMin: 0, lngMax: 0
        };

        if (coordinates.length > 0) {
          coordinateBounds = {
            latMin: Math.min(...coordinates.map(c => c.lat)),
            latMax: Math.max(...coordinates.map(c => c.lat)),
            lngMin: Math.min(...coordinates.map(c => c.lng)),
            lngMax: Math.max(...coordinates.map(c => c.lng))
          };
        }

        // Calculate pincode range
        const sortedPincodes = Array.from(pincodes).sort();
        const pincodeRange = {
          min: sortedPincodes[0] || '',
          max: sortedPincodes[sortedPincodes.length - 1] || '',
          unique: pincodes.size
        };

        const summary: ValidationSummary = {
          totalRows: records.length,
          validRows,
          invalidRows,
          errors,
          warnings,
          pincodeRange,
          coordinateBounds
        };

        resolve(summary);
      })
      .on('error', (error) => {
        logger.error(`Error reading CSV file: ${error.message}`);
        reject(error);
      });
  });
}

/**
 * Displays validation results
 */
function displayResults(summary: ValidationSummary) {
  console.log('\n📊 CSV Validation Results');
  console.log('========================');

  // Basic statistics
  logger.info(`Total rows: ${summary.totalRows}`);
  logger.success(`Valid rows: ${summary.validRows}`);

  if (summary.invalidRows > 0) {
    logger.error(`Invalid rows: ${summary.invalidRows}`);
  } else {
    logger.success('No invalid rows found');
  }

  // Pincode analysis
  console.log('\n📍 Pincode Analysis');
  console.log('------------------');
  logger.info(`Unique pincodes: ${summary.pincodeRange.unique}`);
  logger.info(`Pincode range: ${summary.pincodeRange.min} - ${summary.pincodeRange.max}`);
  logger.success('All pincodes are valid 6-digit Indian postal codes');

  // Coordinate analysis
  console.log('\n🗺️  Coordinate Analysis');
  console.log('----------------------');
  logger.info(`Latitude range: ${summary.coordinateBounds.latMin.toFixed(6)} to ${summary.coordinateBounds.latMax.toFixed(6)}`);
  logger.info(`Longitude range: ${summary.coordinateBounds.lngMin.toFixed(6)} to ${summary.coordinateBounds.lngMax.toFixed(6)}`);

  // Check if coordinates are in Hyderabad area (approximate bounds)
  const hyderabadBounds = {
    latMin: 17.2, latMax: 17.7,
    lngMin: 78.2, lngMax: 78.7
  };

  if (summary.coordinateBounds.latMin >= hyderabadBounds.latMin &&
      summary.coordinateBounds.latMax <= hyderabadBounds.latMax &&
      summary.coordinateBounds.lngMin >= hyderabadBounds.lngMin &&
      summary.coordinateBounds.lngMax <= hyderabadBounds.lngMax) {
    logger.success('Coordinates appear to be in Hyderabad area');
  } else {
    logger.warning('Some coordinates may be outside Hyderabad area');
  }

  // Display warnings
  if (summary.warnings.length > 0) {
    console.log('\n⚠️  Warnings');
    console.log('------------');
    summary.warnings.forEach(warning => logger.warning(warning));
  }

  // Display errors
  if (summary.errors.length > 0) {
    console.log('\n❌ Validation Errors');
    console.log('-------------------');
    summary.errors.slice(0, 10).forEach(error => logger.error(error));

    if (summary.errors.length > 10) {
      logger.warning(`... and ${summary.errors.length - 10} more errors`);
    }
  }

  // Final recommendation
  console.log('\n🎯 Recommendation');
  console.log('----------------');

  if (summary.invalidRows === 0) {
    logger.success('✅ CSV file is valid and ready for import!');
    logger.info('You can now run the update script:');
    console.log(`   npm run update:properties:dry -- --csv-file=${csvFileName}`);
  } else {
    logger.error('❌ CSV file has validation errors that need to be fixed');
    logger.info('Please fix the errors above before running the update script');
  }
}

/**
 * Display help information
 */
function displayHelp() {
  console.log(`
📋 CSV Validation Script
========================

This script validates CSV file format and data integrity before running
the main properties update script.

Usage:
  npm run validate:csv [options]
  npx tsx src/scripts/validatePropertiesCSV.ts [options]

Options:
  --csv-file=<path>      Path to CSV file (default: hyderabad_properties_500010-500040.csv)
  --input=<path>         Alternative syntax for CSV file path
  --help                 Display this help message

Examples:
  # Validate default CSV file
  npm run validate:csv

  # Validate custom CSV file
  npm run validate:csv -- --csv-file=my-properties.csv

  # Direct execution with custom file
  npx tsx src/scripts/validatePropertiesCSV.ts --csv-file=data.csv

Features:
  ✅ Validates CSV file format and structure
  ✅ Checks data integrity and types
  ✅ Analyzes pincode and coordinate ranges
  ✅ Detects potential duplicate entries
  ✅ Provides detailed statistics and recommendations

CSV File Requirements:
  - Columns: communityname, address, pincode, latitude, longitude
  - Pincode: 6-digit Indian postal codes
  - Coordinates: Valid latitude (-90 to 90) and longitude (-180 to 180)
  - Community name: Max 200 characters
  - Address: Max 500 characters

For more information, see the project documentation.
`);
}

// Check for help flag
if (args.includes('--help')) {
  displayHelp();
  process.exit(0);
}

// Main execution
(async () => {
  try {
    logger.info(`Validating CSV file: ${csvFileName}`);
    const summary = await validateCSVFile();
    displayResults(summary);

    // Exit with appropriate code
    process.exit(summary.invalidRows > 0 ? 1 : 0);
  } catch (error: any) {
    logger.error(`Validation failed: ${error.message}`);
    process.exit(1);
  }
})();
