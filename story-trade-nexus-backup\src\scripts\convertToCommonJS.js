/**
 * <PERSON><PERSON>t to convert TypeScript files to CommonJS format for direct execution with Node.js
 */

const fs = require('fs');
const path = require('path');

// Files to convert
const files = [
  'testPincodeLookup.ts',
  'listBookPincodes.ts',
  'updateBookLocationsFromPincodes.ts'
];

// Directory paths
const scriptsDir = path.join(__dirname);
const outputDir = path.join(__dirname, '..', '..', 'scripts');

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Function to convert import statements to require statements
function convertImports(content) {
  // Convert ES module imports to CommonJS requires
  content = content.replace(/import\s+\{\s*([^}]+)\s*\}\s+from\s+['"]([^'"]+)['"]/g, (match, imports, module) => {
    const importNames = imports.split(',').map(name => name.trim());
    const requirePath = module.startsWith('.') ? module : `./${module}`;
    
    return `const { ${importNames.join(', ')} } = require('${requirePath}')`;
  });
  
  // Convert default imports
  content = content.replace(/import\s+(\w+)\s+from\s+['"]([^'"]+)['"]/g, (match, name, module) => {
    const requirePath = module.startsWith('.') ? module : `./${module}`;
    return `const ${name} = require('${requirePath}')`;
  });
  
  // Convert export statements
  content = content.replace(/export\s+const\s+(\w+)/g, 'const $1');
  content = content.replace(/export\s+function\s+(\w+)/g, 'function $1');
  content = content.replace(/export\s+interface\s+(\w+)/g, 'class $1');
  content = content.replace(/export\s+type\s+(\w+)/g, '// type $1');
  content = content.replace(/export\s+enum\s+(\w+)/g, 'const $1');
  
  // Add module.exports at the end
  const exportMatches = content.match(/export\s+\{\s*([^}]+)\s*\}/);
  if (exportMatches) {
    const exportNames = exportMatches[1].split(',').map(name => name.trim());
    content = content.replace(/export\s+\{\s*([^}]+)\s*\}/, `module.exports = { ${exportNames.join(', ')} }`);
  }
  
  return content;
}

// Function to convert TypeScript types to JavaScript comments
function convertTypes(content) {
  // Remove type annotations
  content = content.replace(/:\s*[A-Za-z<>[\]|&]+/g, '');
  content = content.replace(/<[A-Za-z<>[\]|&]+>/g, '');
  
  // Remove interface definitions
  content = content.replace(/interface\s+\w+\s*\{[\s\S]*?\}/g, '');
  
  // Remove type definitions
  content = content.replace(/type\s+\w+\s*=[\s\S]*?;/g, '');
  
  return content;
}

// Process each file
files.forEach(file => {
  const inputPath = path.join(scriptsDir, file);
  const outputPath = path.join(outputDir, file.replace('.ts', '.js'));
  
  console.log(`Converting ${inputPath} to ${outputPath}`);
  
  try {
    // Read the file
    let content = fs.readFileSync(inputPath, 'utf8');
    
    // Convert imports and exports
    content = convertImports(content);
    
    // Convert TypeScript types
    content = convertTypes(content);
    
    // Write the converted file
    fs.writeFileSync(outputPath, content);
    
    console.log(`Successfully converted ${file}`);
  } catch (error) {
    console.error(`Error converting ${file}:`, error);
  }
});

console.log('Conversion complete!');
