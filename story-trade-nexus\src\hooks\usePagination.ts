/**
 * usePagination Hook
 * 
 * Custom hook for managing pagination state with URL synchronization
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useSearchParams, useLocation } from 'react-router-dom';
import { 
  calculatePagination, 
  validatePageNumber, 
  buildPaginationSearchParams,
  PaginationInfo 
} from '@/lib/paginationUtils';

interface UsePaginationOptions {
  itemsPerPage?: number;
  resetOnDependencyChange?: boolean;
}

interface UsePaginationReturn {
  currentPage: number;
  pagination: PaginationInfo;
  goToPage: (page: number) => void;
  goToNextPage: () => void;
  goToPreviousPage: () => void;
  resetToFirstPage: () => void;
  updateTotalItems: (totalItems: number) => void;
}

/**
 * Hook for managing pagination with URL synchronization
 */
export const usePagination = (
  totalItems: number,
  dependencies: any[] = [],
  options: UsePaginationOptions = {}
): UsePaginationReturn => {
  const { itemsPerPage = 12, resetOnDependencyChange = true } = options;
  const [searchParams, setSearchParams] = useSearchParams();
  const location = useLocation();
  
  // Get current page from URL params
  const pageFromUrl = validatePageNumber(searchParams.get('page'));
  const [currentPage, setCurrentPage] = useState(pageFromUrl);
  const [totalItemsState, setTotalItemsState] = useState(totalItems);
  
  // Calculate pagination info
  const pagination = useMemo(() => 
    calculatePagination(totalItemsState, currentPage, itemsPerPage),
    [totalItemsState, currentPage, itemsPerPage]
  );
  
  // Sync with URL params when they change
  useEffect(() => {
    const urlPage = validatePageNumber(searchParams.get('page'));
    if (urlPage !== currentPage) {
      setCurrentPage(urlPage);
    }
  }, [searchParams, currentPage]);
  
  // Update total items when prop changes
  useEffect(() => {
    setTotalItemsState(totalItems);
  }, [totalItems]);
  
  // Reset to first page when dependencies change (e.g., search, filters)
  useEffect(() => {
    if (resetOnDependencyChange && dependencies.length > 0) {
      const shouldReset = currentPage > 1;
      if (shouldReset) {
        resetToFirstPage();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies);
  
  // Ensure current page doesn't exceed total pages
  useEffect(() => {
    if (currentPage > pagination.totalPages && pagination.totalPages > 0) {
      goToPage(pagination.totalPages);
    }
  }, [currentPage, pagination.totalPages]);
  
  // Navigate to specific page
  const goToPage = useCallback((page: number) => {
    const validPage = Math.max(1, Math.min(page, pagination.totalPages || 1));
    
    if (validPage !== currentPage) {
      setCurrentPage(validPage);
      
      // Update URL
      const newParams = buildPaginationSearchParams(searchParams, validPage);
      setSearchParams(newParams, { replace: true });
    }
  }, [currentPage, pagination.totalPages, searchParams, setSearchParams]);
  
  // Navigate to next page
  const goToNextPage = useCallback(() => {
    if (pagination.hasNextPage) {
      goToPage(currentPage + 1);
    }
  }, [currentPage, pagination.hasNextPage, goToPage]);
  
  // Navigate to previous page
  const goToPreviousPage = useCallback(() => {
    if (pagination.hasPreviousPage) {
      goToPage(currentPage - 1);
    }
  }, [currentPage, pagination.hasPreviousPage, goToPage]);
  
  // Reset to first page
  const resetToFirstPage = useCallback(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
      
      // Remove page param from URL (defaults to page 1)
      const newParams = buildPaginationSearchParams(searchParams, 1, true);
      setSearchParams(newParams, { replace: true });
    }
  }, [currentPage, searchParams, setSearchParams]);
  
  // Update total items (useful for dynamic content)
  const updateTotalItems = useCallback((newTotalItems: number) => {
    setTotalItemsState(newTotalItems);
  }, []);
  
  return {
    currentPage,
    pagination,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    resetToFirstPage,
    updateTotalItems
  };
};

/**
 * Hook for pagination with search and filter dependencies
 * Automatically resets to page 1 when search or filters change
 */
export const useBrowsePagination = (
  totalItems: number,
  searchQuery: string,
  selectedGenre: string,
  selectedAvailability: string,
  sortCriteria: string
) => {
  return usePagination(
    totalItems,
    [searchQuery, selectedGenre, selectedAvailability, sortCriteria],
    { itemsPerPage: 12, resetOnDependencyChange: true }
  );
};
