/**
 * BooksPagination Component
 * 
 * Reusable pagination component for book listings
 */

import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Button } from '@/components/ui/button';
import { PaginationInfo, getPaginationDisplayText, getPageAriaLabel } from '@/lib/paginationUtils';
import { cn } from '@/lib/utils';

interface BooksPaginationProps {
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onNextPage: () => void;
  onPreviousPage: () => void;
  className?: string;
  showInfo?: boolean;
  compact?: boolean;
}

const BooksPagination: React.FC<BooksPaginationProps> = ({
  pagination,
  onPageChange,
  onNextPage,
  onPreviousPage,
  className,
  showInfo = true,
  compact = false
}) => {
  const {
    currentPage,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    pageNumbers
  } = pagination;

  // Don't render if there's only one page or no items
  if (totalPages <= 1) {
    return showInfo && pagination.totalItems > 0 ? (
      <div className={cn("flex justify-center py-4", className)}>
        <p className="text-sm text-gray-600">
          {getPaginationDisplayText(pagination)}
        </p>
      </div>
    ) : null;
  }

  const handlePageClick = (page: number) => {
    if (page !== currentPage && page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  const handlePreviousClick = () => {
    if (hasPreviousPage) {
      onPreviousPage();
    }
  };

  const handleNextClick = () => {
    if (hasNextPage) {
      onNextPage();
    }
  };

  return (
    <div className={cn("flex flex-col items-center space-y-4 py-6", className)}>
      {/* Pagination Info */}
      {showInfo && (
        <div className="text-sm text-gray-600 text-center">
          {getPaginationDisplayText(pagination)}
        </div>
      )}

      {/* Pagination Controls */}
      <Pagination>
        <PaginationContent>
          {/* Previous Button */}
          <PaginationItem>
            <PaginationPrevious
              onClick={handlePreviousClick}
              className={cn(
                "cursor-pointer",
                !hasPreviousPage && "pointer-events-none opacity-50"
              )}
              aria-disabled={!hasPreviousPage}
            />
          </PaginationItem>

          {/* Page Numbers */}
          {!compact && pageNumbers.map((pageNum, index) => (
            <PaginationItem key={index}>
              {pageNum === -1 ? (
                <PaginationEllipsis />
              ) : (
                <PaginationLink
                  onClick={() => handlePageClick(pageNum)}
                  isActive={pageNum === currentPage}
                  className="cursor-pointer"
                  aria-label={getPageAriaLabel(pageNum, totalPages)}
                >
                  {pageNum}
                </PaginationLink>
              )}
            </PaginationItem>
          ))}

          {/* Compact mode: show current page info */}
          {compact && (
            <PaginationItem>
              <span className="px-3 py-2 text-sm">
                Page {currentPage} of {totalPages}
              </span>
            </PaginationItem>
          )}

          {/* Next Button */}
          <PaginationItem>
            <PaginationNext
              onClick={handleNextClick}
              className={cn(
                "cursor-pointer",
                !hasNextPage && "pointer-events-none opacity-50"
              )}
              aria-disabled={!hasNextPage}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>

      {/* Mobile-friendly quick navigation */}
      <div className="flex sm:hidden items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handlePreviousClick}
          disabled={!hasPreviousPage}
          className="flex items-center space-x-1"
        >
          <ChevronLeft className="h-4 w-4" />
          <span>Previous</span>
        </Button>
        
        <span className="px-3 py-1 text-sm bg-gray-100 rounded">
          {currentPage} / {totalPages}
        </span>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleNextClick}
          disabled={!hasNextPage}
          className="flex items-center space-x-1"
        >
          <span>Next</span>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default BooksPagination;
