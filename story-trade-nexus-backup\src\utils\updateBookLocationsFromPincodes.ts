/**
 * Utility to update book locations based on pincodes
 */

import { initializeFirebase, db } from '../lib/firebase';
import { lookupPincodeLocation, delay } from '../lib/pincodeService';
import { LocationData } from '../lib/geolocationUtils';

interface UpdateResult {
  success: boolean;
  updatedCount: number;
  errorCount: number;
  message: string;
  updatedBooks: string[];
  failedBooks: string[];
}

/**
 * Updates all books in the database with location information based on their pincodes
 * @returns Promise<UpdateResult> - Result of the update operation
 */
export const updateBooksWithPincodeLocations = async (): Promise<UpdateResult> => {
  try {
    console.log('Starting to update books with location information from pincodes...');
    
    // Initialize Firebase
    await initializeFirebase();
    
    // Dynamically import Firestore functions
    const { 
      collection, 
      getDocs, 
      doc, 
      updateDoc, 
      writeBatch, 
      query, 
      where, 
      serverTimestamp 
    } = await import('firebase/firestore');
    
    // Fetch all books from Firestore
    const booksRef = collection(db, 'books');
    const booksSnapshot = await getDocs(booksRef);
    
    if (booksSnapshot.empty) {
      return {
        success: false,
        updatedCount: 0,
        errorCount: 0,
        message: 'No books found in the database',
        updatedBooks: [],
        failedBooks: []
      };
    }
    
    console.log(`Found ${booksSnapshot.size} books in the database`);
    
    // Track books with pincodes but without location information
    const booksToUpdate: { id: string; title: string; pincode: string }[] = [];
    
    // First pass: identify books that need updating
    booksSnapshot.forEach(bookDoc => {
      const data = bookDoc.data();
      const pincode = data.ownerPincode;
      
      // Check if the book has a pincode but incomplete location information
      if (pincode && 
          (!data.ownerLocation || 
           data.ownerLocation === 'Unknown Location' || 
           !data.ownerCoordinates)) {
        booksToUpdate.push({
          id: bookDoc.id,
          title: data.title || 'Unknown Title',
          pincode: pincode
        });
      }
    });
    
    console.log(`Found ${booksToUpdate.length} books that need location updates`);
    
    if (booksToUpdate.length === 0) {
      return {
        success: true,
        updatedCount: 0,
        errorCount: 0,
        message: 'No books need location updates',
        updatedBooks: [],
        failedBooks: []
      };
    }
    
    // Process books in batches to avoid overwhelming the Firestore API
    const BATCH_SIZE = 10;
    let batch = writeBatch(db);
    let batchSize = 0;
    let batchCount = 0;
    let updatedCount = 0;
    let errorCount = 0;
    const updatedBooks: string[] = [];
    const failedBooks: string[] = [];
    
    // Process each book that needs updating
    for (const book of booksToUpdate) {
      try {
        console.log(`Processing book "${book.title}" (${book.id}) with pincode ${book.pincode}`);
        
        // Lookup location information from pincode
        const locationData = await lookupPincodeLocation(book.pincode);
        
        // If location data is found, update the book
        if (locationData) {
          const bookRef = doc(db, 'books', book.id);
          
          const updateData: Record<string, any> = {
            ownerLocation: `${locationData.city}, ${locationData.state}, ${locationData.pincode}`,
            updatedAt: serverTimestamp()
          };
          
          batch.update(bookRef, updateData);
          batchSize++;
          updatedCount++;
          updatedBooks.push(book.id);
          
          console.log(`Added book "${book.title}" to batch - New location: ${updateData.ownerLocation}`);
          
          // If we've reached the batch size limit, commit the batch and start a new one
          if (batchSize >= BATCH_SIZE) {
            console.log(`Committing batch ${batchCount + 1} with ${batchSize} updates`);
            await batch.commit();
            batch = writeBatch(db);
            batchSize = 0;
            batchCount++;
            
            // Add a delay to avoid overwhelming the API
            await delay(1000);
          }
        } else {
          console.log(`Could not find location data for pincode ${book.pincode}`);
          errorCount++;
          failedBooks.push(book.id);
        }
      } catch (error) {
        console.error(`Error processing book ${book.id}:`, error);
        errorCount++;
        failedBooks.push(book.id);
      }
      
      // Add a delay between pincode lookups to avoid rate limiting
      await delay(1000);
    }
    
    // Commit any remaining updates in the batch
    if (batchSize > 0) {
      console.log(`Committing final batch with ${batchSize} updates`);
      await batch.commit();
      batchCount++;
    }
    
    const message = `Successfully updated ${updatedCount} books with location information in ${batchCount} batches. ${errorCount} errors occurred.`;
    console.log(message);
    
    return {
      success: true,
      updatedCount,
      errorCount,
      message,
      updatedBooks,
      failedBooks
    };
  } catch (error: any) {
    console.error('Error updating books with location information:', error);
    return {
      success: false,
      updatedCount: 0,
      errorCount: 1,
      message: `Error updating books: ${error.message}`,
      updatedBooks: [],
      failedBooks: []
    };
  }
};
