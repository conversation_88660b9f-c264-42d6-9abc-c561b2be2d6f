// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAgXbuXyBEa9aEPVB4JtLugCbRWK39Vj_c",
  authDomain: "book-share-98f6a.firebaseapp.com",
  projectId: "book-share-98f6a",
  storageBucket: "book-share-98f6a.firebasestorage.app", // Firebase Storage bucket
  messagingSenderId: "216941059965",
  appId: "1:216941059965:web:2e0528a8a018ff959c7614",
  measurementId: "G-NYSPR3K1PY"
};

// Initialize Firebase dynamically
let app;
let auth;
let db;
let storage;
let analytics = null;

// Function to initialize Firebase
const initializeFirebase = async () => {
  try {
    const { initializeApp } = await import('firebase/app');
    const { getAuth } = await import('firebase/auth');
    const { getFirestore } = await import('firebase/firestore');
    const { getStorage } = await import('firebase/storage');

    app = initializeApp(firebaseConfig);
    auth = getAuth(app);
    db = getFirestore(app);
    storage = getStorage(app);

    // Enable Firestore logging in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Firebase running in development mode');
    }

    // Initialize Analytics - only in browser environment
    if (typeof window !== 'undefined') {
      const { getAnalytics } = await import('firebase/analytics');
      analytics = getAnalytics(app);
    }

    console.log('Firebase initialized successfully');
    return { app, auth, db, storage, analytics };
  } catch (error) {
    console.error('Error initializing Firebase:', error);
    throw error;
  }
};

// Initialize Firebase immediately
initializeFirebase().catch(error => {
  console.error('Failed to initialize Firebase:', error);
});

export { initializeFirebase, app, auth, db, storage, analytics };
