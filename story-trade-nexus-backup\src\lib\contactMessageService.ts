/**
 * Service for handling contact messages
 */

import { initializeFirebase, db } from './firebase';

/**
 * Interface for contact message data
 */
export interface ContactMessage {
  id?: string;
  email: string;
  phone: string;
  message: string;
  createdAt: Date | any; // Firestore Timestamp or Date
  isRead: boolean;
  readAt?: Date | any; // Firestore Timestamp or Date
}

/**
 * Submits a new contact message to Firestore
 * @param messageData - The contact message data
 * @returns Promise<string> - The ID of the created message
 */
export const submitContactMessage = async (
  messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'isRead' | 'readAt'>
): Promise<string> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    console.log('Submitting contact message:', messageData);

    // Create a reference to the contact messages collection
    const messagesRef = collection(db, 'contactMessages');

    // Add the message to Firestore
    const newMessage = {
      ...messageData,
      createdAt: serverTimestamp(),
      isRead: false
    };

    const docRef = await addDoc(messagesRef, newMessage);
    console.log('Contact message submitted successfully with ID:', docRef.id);

    return docRef.id;
  } catch (error) {
    console.error('Error submitting contact message:', error);
    throw error;
  }
};

/**
 * Gets all contact messages from Firestore
 * @returns Promise<ContactMessage[]> - Array of contact messages
 */
export const getAllContactMessages = async (): Promise<ContactMessage[]> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, getDocs, orderBy } = await import('firebase/firestore');

    console.log('Fetching all contact messages from Firestore');

    // Create a reference to the contact messages collection
    const messagesRef = collection(db, 'contactMessages');

    // Create a query to get all messages sorted by creation date (newest first)
    const messagesQuery = query(messagesRef, orderBy('createdAt', 'desc'));

    // Execute the query
    const querySnapshot = await getDocs(messagesQuery);

    // Map the query results to an array of contact messages
    const messages: ContactMessage[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      messages.push({
        id: doc.id,
        email: data.email || '',
        phone: data.phone || '',
        message: data.message || '',
        createdAt: data.createdAt,
        isRead: data.isRead || false,
        readAt: data.readAt || null
      });
    });

    console.log(`Fetched ${messages.length} contact messages`);
    return messages;
  } catch (error) {
    console.error('Error fetching contact messages:', error);
    throw error;
  }
};

/**
 * Marks a contact message as read in Firestore
 * @param messageId - The ID of the message to mark as read
 * @returns Promise<void>
 */
export const markMessageAsRead = async (messageId: string): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, updateDoc, serverTimestamp } = await import('firebase/firestore');

    console.log(`Marking message ${messageId} as read`);

    // Create a reference to the message document
    const messageRef = doc(db, 'contactMessages', messageId);

    // Update the message document
    await updateDoc(messageRef, {
      isRead: true,
      readAt: serverTimestamp()
    });

    console.log(`Message ${messageId} marked as read successfully`);
  } catch (error) {
    console.error(`Error marking message ${messageId} as read:`, error);
    throw error;
  }
};

/**
 * Marks a contact message as unread in Firestore
 * @param messageId - The ID of the message to mark as unread
 * @returns Promise<void>
 */
export const markMessageAsUnread = async (messageId: string): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, updateDoc } = await import('firebase/firestore');

    console.log(`Marking message ${messageId} as unread`);

    // Create a reference to the message document
    const messageRef = doc(db, 'contactMessages', messageId);

    // Update the message document
    await updateDoc(messageRef, {
      isRead: false,
      readAt: null
    });

    console.log(`Message ${messageId} marked as unread successfully`);
  } catch (error) {
    console.error(`Error marking message ${messageId} as unread:`, error);
    throw error;
  }
};
