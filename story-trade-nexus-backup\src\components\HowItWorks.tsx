
import React from 'react';
import { <PERSON><PERSON><PERSON>, Refresh<PERSON>w, MessageSquare, Star } from 'lucide-react';

const HowItWorks: React.FC = () => {
  const steps = [
    {
      icon: BookOpen,
      title: 'List Your Books',
      description: 'Add your used books to our platform with details like condition, perceived value, and availability options.',
    },
    {
      icon: RefreshCw,
      title: 'Exchange, Rent or Sell',
      description: 'Choose how you want to share your books - swap for another book, rent it out, or sell it to another reader.',
    },
    {
      icon: MessageSquare,
      title: 'Connect & Arrange',
      description: 'Chat with interested readers, agree on terms, and arrange for book handover or shipping.',
    },
    {
      icon: Star,
      title: 'Rate & Review',
      description: 'After a successful transaction, rate your experience to help build trust in our community.',
    },
  ];

  return (
    <section className="py-16 bg-beige-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-playfair font-bold text-navy-800 mb-3">How PeerBooks Works</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Our platform makes it easy to connect with fellow book lovers and share your literary treasures.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="bg-white rounded-lg p-6 shadow-md relative">
              <div className="absolute -top-5 left-1/2 transform -translate-x-1/2 bg-burgundy-500 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold">
                {index + 1}
              </div>
              <div className="mt-4 text-center">
                <div className="bg-beige-200 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <step.icon className="h-8 w-8 text-burgundy-500" />
                </div>
                <h3 className="font-playfair font-bold text-lg text-navy-700 mb-2">{step.title}</h3>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-navy-500 text-white p-8 rounded-lg shadow-lg">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-2/3 mb-6 md:mb-0 md:pr-6">
              <h3 className="text-2xl font-playfair font-bold mb-4">Our Value-Matching System</h3>
              <p className="mb-4">
                We've created a unique system that helps match books based on perceived value, making exchanges fair and transparent:
              </p>
              <ul className="list-disc list-inside space-y-2 text-beige-100">
                <li>Each book gets assigned a value (1-10) by its owner</li>
                <li>Our system matches books with similar values for equitable exchanges</li>
                <li>If there's a value gap, users can negotiate a small fee to balance the exchange</li>
                <li>No middleman fees - all negotiations happen directly between readers</li>
              </ul>
            </div>
            <div className="md:w-1/3">
              <div className="bg-burgundy-500/20 border border-burgundy-500/50 rounded-lg p-6">
                <div className="flex justify-between items-center mb-4">
                  <div className="text-sm">Book A: Harry Potter</div>
                  <div className="text-sm font-bold">Value: 8/10</div>
                </div>
                <div className="flex items-center justify-center my-3">
                  <RefreshCw className="h-10 w-10 text-beige-500" />
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm">Book B: 1984</div>
                  <div className="text-sm font-bold">Value: 7/10</div>
                </div>
                <div className="mt-3 pt-3 border-t border-white/20 text-center text-sm">
                  <span className="font-medium">Difference:</span> 1 point
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
