/**
 * LazyCarousel Component
 * 
 * Lazy-loaded wrapper for embla-carousel to reduce initial bundle size
 */

import React, { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Lazy load the carousel component
const Carousel = React.lazy(() => import('@/components/ui/carousel').then(module => ({
  default: module.Carousel
})));

const CarouselContent = React.lazy(() => import('@/components/ui/carousel').then(module => ({
  default: module.CarouselContent
})));

const CarouselItem = React.lazy(() => import('@/components/ui/carousel').then(module => ({
  default: module.CarouselItem
})));

const CarouselPrevious = React.lazy(() => import('@/components/ui/carousel').then(module => ({
  default: module.CarouselPrevious
})));

const CarouselNext = React.lazy(() => import('@/components/ui/carousel').then(module => ({
  default: module.CarouselNext
})));

// Carousel loading skeleton
const CarouselSkeleton: React.FC<{ height?: number; itemCount?: number }> = ({ 
  height = 200, 
  itemCount = 3 
}) => (
  <div className="w-full space-y-4">
    <div className="flex space-x-4 overflow-hidden">
      {Array.from({ length: itemCount }).map((_, index) => (
        <Skeleton 
          key={index} 
          className="flex-shrink-0 rounded-lg" 
          style={{ width: '300px', height: `${height}px` }}
        />
      ))}
    </div>
    <div className="flex justify-center space-x-2">
      <Skeleton className="w-8 h-8 rounded-full" />
      <Skeleton className="w-8 h-8 rounded-full" />
    </div>
  </div>
);

// Lazy carousel wrapper components
interface LazyCarouselProps {
  children: React.ReactNode;
  className?: string;
  opts?: any;
  orientation?: "horizontal" | "vertical";
  setApi?: (api: any) => void;
  fallback?: React.ReactNode;
}

export const LazyCarousel: React.FC<LazyCarouselProps> = ({ 
  children, 
  fallback,
  ...props 
}) => (
  <Suspense fallback={fallback || <CarouselSkeleton />}>
    <Carousel {...props}>
      {children}
    </Carousel>
  </Suspense>
);

interface LazyCarouselContentProps {
  children: React.ReactNode;
  className?: string;
  fallback?: React.ReactNode;
}

export const LazyCarouselContent: React.FC<LazyCarouselContentProps> = ({ 
  children, 
  fallback,
  ...props 
}) => (
  <Suspense fallback={fallback || <div className="flex space-x-4">{children}</div>}>
    <CarouselContent {...props}>
      {children}
    </CarouselContent>
  </Suspense>
);

interface LazyCarouselItemProps {
  children: React.ReactNode;
  className?: string;
  fallback?: React.ReactNode;
}

export const LazyCarouselItem: React.FC<LazyCarouselItemProps> = ({ 
  children, 
  fallback,
  ...props 
}) => (
  <Suspense fallback={fallback || <div className="flex-shrink-0">{children}</div>}>
    <CarouselItem {...props}>
      {children}
    </CarouselItem>
  </Suspense>
);

interface LazyCarouselControlProps {
  className?: string;
  fallback?: React.ReactNode;
}

export const LazyCarouselPrevious: React.FC<LazyCarouselControlProps> = ({ 
  fallback,
  ...props 
}) => (
  <Suspense fallback={fallback || <Skeleton className="w-8 h-8 rounded-full" />}>
    <CarouselPrevious {...props} />
  </Suspense>
);

export const LazyCarouselNext: React.FC<LazyCarouselControlProps> = ({ 
  fallback,
  ...props 
}) => (
  <Suspense fallback={fallback || <Skeleton className="w-8 h-8 rounded-full" />}>
    <CarouselNext {...props} />
  </Suspense>
);

// Export skeleton for external use
export { CarouselSkeleton };

// Default export for convenience
export default {
  Carousel: LazyCarousel,
  CarouselContent: LazyCarouselContent,
  CarouselItem: LazyCarouselItem,
  CarouselPrevious: LazyCarouselPrevious,
  CarouselNext: LazyCarouselNext,
  CarouselSkeleton
};
