import React from 'react';

interface RupeeSymbolProps {
  amount: number;
  className?: string;
}

/**
 * A component that displays an amount in Indian Rupees
 * Uses plain text "Rs." which works in any encoding
 */
const RupeeSymbol: React.FC<RupeeSymbolProps> = ({ amount, className = '' }) => {
  // Format the amount with commas in Indian numbering system (if needed)
  const formattedAmount = amount.toLocaleString('en-IN');

  return (
    <span className={className}>
      Rs. {formattedAmount}
    </span>
  );
};

export default RupeeSymbol;
