import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/AuthContext';
import AdminLayout from '@/components/layout/AdminLayout';

const AdminDashboard: React.FC = () => {
  const { currentUser } = useAuth();

  return (
    <AdminLayout>
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h1 className="text-2xl font-bold text-navy-800 mb-2">Admin Dashboard</h1>
        <p className="text-gray-600 mb-4">
          Welcome back, {currentUser?.displayName || 'Admin'}. Manage your book-sharing platform from here.
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <p className="text-blue-800 text-sm">
            This is a restricted area. Please be careful when making changes to the system.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-navy-800 mb-4">Book Approvals</h2>
          <p className="text-gray-600 mb-4">
            Review and approve new book submissions from users.
          </p>
          <Link to="/admin/books">
            <Button>Manage Book Approvals</Button>
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-navy-800 mb-4">User Management</h2>
          <p className="text-gray-600 mb-4">
            Manage user accounts, roles, and permissions.
          </p>
          <Link to="/admin/users">
            <Button>Manage Users</Button>
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-navy-800 mb-4">Contact Messages</h2>
          <p className="text-gray-600 mb-4">
            View and respond to messages from users.
          </p>
          <Link to="/admin/messages">
            <Button>View Messages</Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-navy-800 mb-4">Admin Tools</h2>
          <p className="text-gray-600 mb-4">
            Access administrative utilities and maintenance tools.
          </p>
          <Link to="/admin/utilities">
            <Button>Access Tools</Button>
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-navy-800 mb-4">Admin Settings</h2>
          <p className="text-gray-600 mb-4">
            Configure admin preferences and system settings.
          </p>
          <Link to="/admin/settings">
            <Button>Configure Settings</Button>
          </Link>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
