# Firebase Community Query Tool

A Node.js command-line script that queries the Firebase Firestore database to retrieve and display community names based on a user-provided pincode input.

## Features

- 🔍 **Pincode-based Search**: Query communities by 6-digit pincode
- 🏘️ **Unique Results**: Automatically removes duplicate community names
- 📊 **Detailed Output**: Shows search results with counts and numbered lists
- ⚡ **Performance Optimized**: Implements query limits to prevent excessive data retrieval
- 🛡️ **Error Handling**: Comprehensive error handling with helpful messages
- 📱 **User Friendly**: Clean, readable terminal output with emojis and formatting

## Prerequisites

- Node.js (v14 or higher)
- npm (Node Package Manager)
- Access to the Firebase project 'book-share-98f6a'
- Internet connection

## Installation

1. **Install Dependencies**:
   ```bash
   npm install firebase
   ```

   Or if you want to use the provided package.json:
   ```bash
   # Copy the package.json file
   cp query-script-package.json package.json

   # Install dependencies
   npm install
   ```

2. **Make the script executable** (optional, for Unix-like systems):
   ```bash
   chmod +x queryCommunities.js
   ```

## Usage

### Basic Usage

```bash
node queryCommunities.js <pincode>
```

### Examples

```bash
# Query communities for pincode 500001
node queryCommunities.js 500001

# Query communities for pincode 560001
node queryCommunities.js 560001

# Query communities for pincode 400001
node queryCommunities.js 400001
```

### Using npm scripts (if using the provided package.json)

```bash
# Run the query tool
npm run query 500001

# Show help
npm run help
```

### Using Helper Scripts

For convenience, helper scripts are provided:

**Windows (Batch Script):**
```cmd
query.bat 500001
```

**Unix/Linux/macOS (Shell Script):**
```bash
# Make executable (first time only)
chmod +x query.sh

# Run the script
./query.sh 500001
```

### Testing the Script

A test suite is provided to verify the script functionality:

```bash
# Run the test suite
node testQuery.js
```

The test suite will:
- Test valid and invalid pincode formats
- Verify help and version flags work correctly
- Check error handling for various scenarios
- Provide a summary of test results

## Input Requirements

- **Pincode Format**: Must be exactly 6 digits
- **No Special Characters**: Only numeric digits allowed
- **No Spaces**: Remove any spaces from the pincode

### Valid Examples
- ✅ `500001`
- ✅ `560001`
- ✅ `400001`

### Invalid Examples
- ❌ `50001` (only 5 digits)
- ❌ `5000011` (7 digits)
- ❌ `500 001` (contains space)
- ❌ `500-001` (contains hyphen)
- ❌ `abc123` (contains letters)

## Output Format

The script provides detailed, formatted output including:

```
🏘️  Firebase Community Query Tool
📍 Searching Hyderabad Properties Database

ℹ️  Starting search for pincode: 500001
ℹ️  Connecting to Firebase Firestore...
ℹ️  Querying 'hyderabadProperties' collection for pincode: 500001
ℹ️  Executing query 1/2 (pincode as number)...
✅ Found 15 documents with pincode as number
ℹ️  Executing query 2/2 (pincode as string)...
ℹ️  No documents found with pincode as string
📊 Total documents processed: 15

============================================================
🔍 SEARCH RESULTS FOR PINCODE: 500001
============================================================
✅ Found 8 unique communities

📍 COMMUNITIES:
   1. Banjara Hills
   2. Jubilee Hills
   3. Madhapur
   4. Gachibowli
   5. Hitech City
   6. Kondapur
   7. Miyapur
   8. Kukatpally
============================================================
```

## Error Handling

The script handles various error scenarios:

### Invalid Pincode Format
```bash
❌ Invalid pincode format

📋 Pincode Requirements:
  • Must be exactly 6 digits
  • No spaces, letters, or special characters
  • Your input: "50001"
```

### No Results Found
```bash
⚠️  No communities found for this pincode

💡 Suggestions:
  • Verify the pincode is correct
  • Try a nearby pincode
  • Check if the area is covered in our database
```

### Database Connection Issues
```bash
❌ Script execution failed: permission-denied

💡 This might be a permissions issue. Please check:
  • Firebase project configuration
  • Firestore security rules
  • Network connectivity
```

## Technical Details

### Database Configuration
- **Firebase Project**: book-share-98f6a
- **Collection**: hyderabadProperties
- **Query Field**: pincode
- **Result Field**: communityName

### Performance Features
- **Query Limit**: 100 documents per query to prevent excessive data retrieval
- **Dual Format Support**: Queries both string and number pincode formats
- **Duplicate Removal**: Uses Set data structure for automatic deduplication
- **Sorted Results**: Communities are displayed in alphabetical order

### Data Processing
1. Validates pincode format (6 digits)
2. Connects to Firebase Firestore
3. Executes queries for both number and string pincode formats
4. Processes results to extract unique community names
5. Sorts communities alphabetically
6. Displays formatted results

## Troubleshooting

### Common Issues

1. **"Module not found" error**:
   ```bash
   npm install firebase
   ```

2. **"Permission denied" error**:
   - Check Firebase project access
   - Verify Firestore security rules
   - Ensure internet connectivity

3. **"Collection not found" error**:
   - Verify the hyderabadProperties collection exists
   - Check if data has been imported to Firestore

4. **No results for valid pincode**:
   - The pincode might not be in the database
   - Try nearby pincodes
   - Check if the area is covered in the dataset

### Getting Help

If you encounter issues:
1. Check the error messages for specific guidance
2. Verify your internet connection
3. Ensure the Firebase project is accessible
4. Confirm the hyderabadProperties collection exists and has data

## Script Architecture

The script is organized into several key functions:

- `validatePincode()`: Validates input format
- `queryCommunities()`: Executes Firestore queries
- `displayResults()`: Formats and displays output
- `main()`: Orchestrates the entire process

This modular approach makes the script maintainable and easy to extend.
