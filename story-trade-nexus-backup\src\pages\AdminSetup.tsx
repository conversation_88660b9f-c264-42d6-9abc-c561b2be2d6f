import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Shield, Check, RefreshCw } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import { setupDirectAdmin } from '@/lib/userService';
import { toast } from 'sonner';

const AdminSetup: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string>('');
  const [logs, setLogs] = useState<string[]>([]);

  // Automatically run the setup function when the page loads
  useEffect(() => {
    handleSetupAdmin();
  }, []);

  // Function to capture console logs
  useEffect(() => {
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;

    console.log = (...args) => {
      originalConsoleLog(...args);
      setLogs(prev => [...prev, `LOG: ${args.map(arg => String(arg)).join(' ')}`]);
    };

    console.error = (...args) => {
      originalConsoleError(...args);
      setLogs(prev => [...prev, `ERROR: ${args.map(arg => String(arg)).join(' ')}`]);
    };

    return () => {
      console.log = originalConsoleLog;
      console.error = originalConsoleError;
    };
  }, []);

  const handleSetupAdmin = async () => {
    if (loading) return; // Don't run if already in progress

    try {
      setLoading(true);
      setError(null);
      setMessage('');
      setLogs([]);

      // Call the function to <NAME_EMAIL> as admin
      const result = await setupDirectAdmin();

      if (result.success) {
        setSuccess(true);
        setMessage(result.message);
        toast.success('Admin user set up successfully!');
      } else {
        setError(result.message);
        toast.error('Failed to set up admin');
      }
    } catch (error) {
      console.error('Error setting up admin:', error);
      setError(`Failed to set up admin: ${error instanceof Error ? error.message : 'Unknown error'}`);
      toast.error('Failed to set up admin');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow flex items-center justify-center py-8">
        <div className="max-w-3xl w-full mx-auto p-8 bg-white rounded-lg shadow-lg">
          <div className="text-center mb-6">
            <Shield className="h-16 w-16 text-burgundy-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-navy-800 mb-2">Admin Setup</h1>
            <p className="text-gray-600">
              <NAME_EMAIL> as an admin user.
            </p>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
              <p className="font-semibold">Error:</p>
              <p>{error}</p>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6 flex items-center justify-center">
              <Check className="h-5 w-5 mr-2" />
              <p>{message || 'Admin user set up successfully!'}</p>
            </div>
          )}

          {loading ? (
            <div className="flex justify-center items-center py-4 mb-6">
              <Spinner size="md" />
              <span className="ml-2 text-gray-600">Setting up admin user...</span>
            </div>
          ) : (
            <div className="flex justify-center mb-6">
              <Button
                onClick={handleSetupAdmin}
                className="flex items-center"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {success ? 'Try Again' : 'Set Up Admin'}
              </Button>
            </div>
          )}

          <div className="mt-8">
            <h2 className="text-lg font-semibold mb-2">Process Logs:</h2>
            <div className="bg-gray-50 border border-gray-200 rounded p-4 max-h-60 overflow-y-auto text-sm font-mono">
              {logs.length > 0 ? (
                logs.map((log, index) => (
                  <div
                    key={index}
                    className={`py-1 ${log.startsWith('ERROR') ? 'text-red-600' : 'text-gray-700'}`}
                  >
                    {log}
                  </div>
                ))
              ) : (
                <p className="text-gray-500 italic">No logs available yet...</p>
              )}
            </div>
          </div>

          <div className="flex justify-center mt-8">
            <Link to="/">
              <Button variant="outline" className="mr-4">
                Return to Home
              </Button>
            </Link>
            <Link to="/admin">
              <Button>
                Go to Admin Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default AdminSetup;
