/**
 * <PERSON><PERSON>t to update book locations from pincodes
 * This is a standalone script that can be run directly with Node.js
 */

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDXQnxRLuSWnhj9QkO-UGOGbxJz0Gqfzz0",
  authDomain: "book-share-98f6a.firebaseapp.com",
  projectId: "book-share-98f6a",
  storageBucket: "book-share-98f6a.appspot.com",
  messagingSenderId: "1051281275473",
  appId: "1:1051281275473:web:c6e3a7d3d1a1de2e3a8f9c"
};

// Import required modules
import { initializeApp } from 'firebase/app';
import {
  getFirestore,
  collection,
  getDocs,
  doc,
  updateDoc,
  writeBatch,
  serverTimestamp
} from 'firebase/firestore';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

/**
 * Lookup location information from a pincode
 * @param {string} pincode - The Indian postal pincode to lookup
 * @returns {Promise<object|null>} - Location information including city, state, etc.
 */
async function lookupPincodeLocation(pincode) {
  try {
    console.log(`Looking up location for pincode: ${pincode}`);

    // Validate pincode format (Indian pincodes are 6 digits)
    if (!/^\d{6}$/.test(pincode)) {
      console.error(`Invalid pincode format: ${pincode}`);
      return null;
    }

    // Since the API is having SSL certificate issues, we'll use a hardcoded mapping for common pincodes
    // In a production environment, you would use a reliable API or a complete database of pincodes
    const pincodeMap = {
      '110001': { city: 'New Delhi', state: 'Delhi', name: 'Connaught Place' },
      '400001': { city: 'Mumbai', state: 'Maharashtra', name: 'Fort' },
      '600001': { city: 'Chennai', state: 'Tamil Nadu', name: 'Park Town' },
      '700001': { city: 'Kolkata', state: 'West Bengal', name: 'Dalhousie Square' },
      '500001': { city: 'Hyderabad', state: 'Telangana', name: 'Afzal Gunj' },
      '380001': { city: 'Ahmedabad', state: 'Gujarat', name: 'Khadia' },
      '560001': { city: 'Bangalore', state: 'Karnataka', name: 'M.G. Road' },
      '226001': { city: 'Lucknow', state: 'Uttar Pradesh', name: 'Hazratganj' },
      '800001': { city: 'Patna', state: 'Bihar', name: 'Gandhi Maidan' },
      '302001': { city: 'Jaipur', state: 'Rajasthan', name: 'Bani Park' }
    };

    // Check if we have the pincode in our map
    if (pincodeMap[pincode]) {
      const info = pincodeMap[pincode];

      // Create location data from the mapped information
      const locationData = {
        city: info.city,
        state: info.state,
        pincode: pincode,
        fullAddress: `${info.name}, ${info.city}, ${info.state}, ${pincode}, India`
      };

      console.log(`Location data for pincode ${pincode}:`, locationData);
      return locationData;
    }

    // For pincodes not in our map, generate a generic location
    // In a production environment, you would use a complete database or a reliable API
    const firstTwoDigits = pincode.substring(0, 2);
    let state = 'Unknown State';

    // Map first two digits to states (simplified mapping)
    const stateMap = {
      '11': 'Delhi',
      '12': 'Haryana',
      '13': 'Punjab',
      '14': 'Punjab',
      '15': 'Himachal Pradesh',
      '16': 'Himachal Pradesh',
      '17': 'Jammu & Kashmir',
      '18': 'Jammu & Kashmir',
      '19': 'Jammu & Kashmir',
      '20': 'Uttar Pradesh',
      '21': 'Uttar Pradesh',
      '22': 'Uttar Pradesh',
      '23': 'Uttar Pradesh',
      '24': 'Uttar Pradesh',
      '25': 'Uttar Pradesh',
      '26': 'Uttar Pradesh',
      '27': 'Uttar Pradesh',
      '28': 'Uttarakhand',
      '30': 'Rajasthan',
      '31': 'Rajasthan',
      '32': 'Rajasthan',
      '33': 'Rajasthan',
      '34': 'Rajasthan',
      '36': 'Gujarat',
      '37': 'Gujarat',
      '38': 'Gujarat',
      '39': 'Gujarat',
      '40': 'Maharashtra',
      '41': 'Maharashtra',
      '42': 'Maharashtra',
      '43': 'Maharashtra',
      '44': 'Maharashtra',
      '45': 'Maharashtra',
      '50': 'Telangana',
      '51': 'Andhra Pradesh',
      '52': 'Andhra Pradesh',
      '53': 'Andhra Pradesh',
      '56': 'Karnataka',
      '57': 'Karnataka',
      '60': 'Tamil Nadu',
      '61': 'Tamil Nadu',
      '62': 'Tamil Nadu',
      '63': 'Tamil Nadu',
      '67': 'Kerala',
      '68': 'Kerala',
      '69': 'Kerala',
      '70': 'West Bengal',
      '71': 'West Bengal',
      '72': 'West Bengal',
      '73': 'West Bengal',
      '74': 'Orissa',
      '75': 'Orissa',
      '78': 'Assam',
      '79': 'Assam',
      '80': 'Bihar',
      '81': 'Bihar',
      '82': 'Bihar',
      '83': 'Jharkhand',
      '84': 'Jharkhand',
      '49': 'Andaman & Nicobar Islands',
      '39': 'Daman & Diu',
      '40': 'Dadra & Nagar Haveli',
      '60': 'Pondicherry',
      '79': 'Arunachal Pradesh',
      '79': 'Nagaland',
      '79': 'Manipur',
      '79': 'Mizoram',
      '79': 'Tripura',
      '79': 'Meghalaya',
      '18': 'Sikkim',
      '90': 'Army Postal Service',
      '99': 'Army Postal Service'
    };

    state = stateMap[firstTwoDigits] || 'Unknown State';

    // Create generic location data
    const locationData = {
      city: `City (Pincode: ${pincode})`,
      state: state,
      pincode: pincode,
      fullAddress: `Unknown Location, ${state}, ${pincode}, India`
    };

    console.log(`Generated generic location data for pincode ${pincode}:`, locationData);
    return locationData;
  } catch (error) {
    console.error(`Error looking up pincode ${pincode}:`, error);
    return null;
  }
}

/**
 * Add a delay between API requests to avoid rate limiting
 * @param {number} ms - Milliseconds to delay
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Updates all books in the database with location information based on their pincodes
 */
async function updateBooksWithPincodeLocations() {
  try {
    console.log('Starting to update books with location information from pincodes...');

    // Fetch all books from Firestore
    const booksRef = collection(db, 'books');
    const booksSnapshot = await getDocs(booksRef);

    if (booksSnapshot.empty) {
      console.log('No books found in the database');
      return;
    }

    console.log(`Found ${booksSnapshot.size} books in the database`);

    // Track books with pincodes but without location information
    const booksToUpdate = [];

    // First pass: identify books that need updating
    booksSnapshot.forEach(bookDoc => {
      const data = bookDoc.data();
      const pincode = data.ownerPincode;

      // Check if the book has a pincode but incomplete location information
      if (pincode &&
          (!data.ownerLocation ||
           data.ownerLocation === 'Unknown Location' ||
           !data.ownerCoordinates)) {
        booksToUpdate.push({
          id: bookDoc.id,
          title: data.title || 'Unknown Title',
          pincode: pincode
        });
      }
    });

    console.log(`Found ${booksToUpdate.length} books that need location updates`);

    if (booksToUpdate.length === 0) {
      console.log('No books need location updates');
      return;
    }

    // Process books in batches to avoid overwhelming the Firestore API
    const BATCH_SIZE = 10;
    let batch = writeBatch(db);
    let batchSize = 0;
    let batchCount = 0;
    let updatedCount = 0;
    let errorCount = 0;

    // Process each book that needs updating
    for (const book of booksToUpdate) {
      try {
        console.log(`Processing book "${book.title}" (${book.id}) with pincode ${book.pincode}`);

        // Lookup location information from pincode
        const locationData = await lookupPincodeLocation(book.pincode);

        // If location data is found, update the book
        if (locationData) {
          const bookRef = doc(db, 'books', book.id);

          const updateData = {
            ownerLocation: `${locationData.city}, ${locationData.state}, ${locationData.pincode}`,
            updatedAt: serverTimestamp()
          };

          batch.update(bookRef, updateData);
          batchSize++;
          updatedCount++;

          console.log(`Added book "${book.title}" to batch - New location: ${updateData.ownerLocation}`);

          // If we've reached the batch size limit, commit the batch and start a new one
          if (batchSize >= BATCH_SIZE) {
            console.log(`Committing batch ${batchCount + 1} with ${batchSize} updates`);
            await batch.commit();
            batch = writeBatch(db);
            batchSize = 0;
            batchCount++;

            // Add a delay to avoid overwhelming the API
            await delay(1000);
          }
        } else {
          console.log(`Could not find location data for pincode ${book.pincode}`);
          errorCount++;
        }
      } catch (error) {
        console.error(`Error processing book ${book.id}:`, error);
        errorCount++;
      }

      // Add a delay between pincode lookups to avoid rate limiting
      await delay(1000);
    }

    // Commit any remaining updates in the batch
    if (batchSize > 0) {
      console.log(`Committing final batch with ${batchSize} updates`);
      await batch.commit();
      batchCount++;
    }

    console.log(`Successfully updated ${updatedCount} books with location information in ${batchCount} batches. ${errorCount} errors occurred.`);
  } catch (error) {
    console.error('Error updating books with location information:', error);
  }
}

// Run the update function
updateBooksWithPincodeLocations()
  .then(() => {
    console.log('Update process complete!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error running update process:', error);
    process.exit(1);
  });
