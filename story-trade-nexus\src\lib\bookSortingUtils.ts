/**
 * Book Sorting Utilities for PeerBooks
 * 
 * This module provides comprehensive sorting functionality for book listings
 * while maintaining the community-first priority system.
 */

import { Book } from '@/types/index';

export type SortCriteria = 
  | 'community-distance' // Default: Community first, then distance
  | 'price-low-high'     // Selling price: low to high
  | 'price-high-low'     // Selling price: high to low
  | 'rental-low-high'    // Rental price: low to high
  | 'rental-high-low'    // Rental price: high to low
  | 'distance'           // Distance only
  | 'newest-first'       // Latest upload: newest first
  | 'oldest-first';      // Latest upload: oldest first

export interface SortOption {
  value: SortCriteria;
  label: string;
  description: string;
}

export const SORT_OPTIONS: SortOption[] = [
  {
    value: 'community-distance',
    label: 'Community + Distance',
    description: 'Same community first, then by distance'
  },
  {
    value: 'price-low-high',
    label: 'Price: Low to High',
    description: 'Selling price from lowest to highest'
  },
  {
    value: 'price-high-low',
    label: 'Price: High to Low',
    description: 'Selling price from highest to lowest'
  },
  {
    value: 'rental-low-high',
    label: 'Rental: Low to High',
    description: 'Rental price from lowest to highest'
  },
  {
    value: 'rental-high-low',
    label: 'Rental: High to Low',
    description: 'Rental price from highest to lowest'
  },
  {
    value: 'distance',
    label: 'Distance',
    description: 'Closest to farthest'
  },
  {
    value: 'newest-first',
    label: 'Newest First',
    description: 'Most recently added books first'
  },
  {
    value: 'oldest-first',
    label: 'Oldest First',
    description: 'Oldest books first'
  }
];

/**
 * Get the effective selling price for a book
 * Handles books with multiple availability types
 */
const getEffectiveSellingPrice = (book: Book): number | null => {
  // Only consider books that are available for sale
  if (!book.availability.includes('Sale') || !book.price) {
    return null;
  }
  return book.price;
};

/**
 * Get the effective rental price for a book
 * Normalizes rental prices to a daily rate for comparison
 */
const getEffectiveRentalPrice = (book: Book): number | null => {
  // Only consider books that are available for rent
  if (!book.availability.includes('Rent') || !book.rentalPrice) {
    return null;
  }

  const price = book.rentalPrice;
  const period = book.rentalPeriod?.toLowerCase() || 'per day';

  // Normalize to daily rate for comparison
  if (period.includes('week')) {
    return price / 7;
  } else if (period.includes('month')) {
    return price / 30;
  } else if (period.includes('year')) {
    return price / 365;
  }
  
  // Default to daily rate
  return price;
};

/**
 * Get the creation date as a comparable number
 */
const getCreationTime = (book: Book): number => {
  const date = book.createdAt instanceof Date ? book.createdAt : new Date(book.createdAt);
  return date.getTime();
};

/**
 * Sort books based on the specified criteria while maintaining community priority
 * 
 * @param books Array of books to sort
 * @param criteria Sorting criteria to apply
 * @param userCommunity Current user's community for priority sorting
 * @returns Sorted array of books
 */
export const sortBooks = (
  books: Book[], 
  criteria: SortCriteria, 
  userCommunity?: string
): Book[] => {
  if (!books || books.length === 0) {
    return [];
  }

  return [...books].sort((a, b) => {
    // For all criteria except pure distance, maintain community priority as primary sort
    if (criteria !== 'distance') {
      const aIsSameCommunity = userCommunity && a.ownerCommunity && a.ownerCommunity === userCommunity;
      const bIsSameCommunity = userCommunity && b.ownerCommunity && b.ownerCommunity === userCommunity;

      // If one book is from same community and other is not, prioritize same community
      if (aIsSameCommunity && !bIsSameCommunity) return -1;
      if (bIsSameCommunity && !aIsSameCommunity) return 1;
    }

    // Apply secondary sorting based on criteria
    switch (criteria) {
      case 'community-distance':
        // Distance sorting within community groups
        if (a.distance !== undefined && b.distance !== undefined) {
          return a.distance - b.distance;
        }
        if (a.distance !== undefined) return -1;
        if (b.distance !== undefined) return 1;
        // Fallback to newest first
        return getCreationTime(b) - getCreationTime(a);

      case 'price-low-high': {
        const priceA = getEffectiveSellingPrice(a);
        const priceB = getEffectiveSellingPrice(b);
        
        // Books with prices come first
        if (priceA !== null && priceB !== null) {
          return priceA - priceB;
        }
        if (priceA !== null) return -1;
        if (priceB !== null) return 1;
        // Fallback to distance then newest first
        if (a.distance !== undefined && b.distance !== undefined) {
          return a.distance - b.distance;
        }
        return getCreationTime(b) - getCreationTime(a);
      }

      case 'price-high-low': {
        const priceA = getEffectiveSellingPrice(a);
        const priceB = getEffectiveSellingPrice(b);
        
        // Books with prices come first
        if (priceA !== null && priceB !== null) {
          return priceB - priceA;
        }
        if (priceA !== null) return -1;
        if (priceB !== null) return 1;
        // Fallback to distance then newest first
        if (a.distance !== undefined && b.distance !== undefined) {
          return a.distance - b.distance;
        }
        return getCreationTime(b) - getCreationTime(a);
      }

      case 'rental-low-high': {
        const rentalA = getEffectiveRentalPrice(a);
        const rentalB = getEffectiveRentalPrice(b);
        
        // Books with rental prices come first
        if (rentalA !== null && rentalB !== null) {
          return rentalA - rentalB;
        }
        if (rentalA !== null) return -1;
        if (rentalB !== null) return 1;
        // Fallback to distance then newest first
        if (a.distance !== undefined && b.distance !== undefined) {
          return a.distance - b.distance;
        }
        return getCreationTime(b) - getCreationTime(a);
      }

      case 'rental-high-low': {
        const rentalA = getEffectiveRentalPrice(a);
        const rentalB = getEffectiveRentalPrice(b);
        
        // Books with rental prices come first
        if (rentalA !== null && rentalB !== null) {
          return rentalB - rentalA;
        }
        if (rentalA !== null) return -1;
        if (rentalB !== null) return 1;
        // Fallback to distance then newest first
        if (a.distance !== undefined && b.distance !== undefined) {
          return a.distance - b.distance;
        }
        return getCreationTime(b) - getCreationTime(a);
      }

      case 'distance':
        // Pure distance sorting (no community priority)
        if (a.distance !== undefined && b.distance !== undefined) {
          return a.distance - b.distance;
        }
        if (a.distance !== undefined) return -1;
        if (b.distance !== undefined) return 1;
        // Fallback to newest first
        return getCreationTime(b) - getCreationTime(a);

      case 'newest-first':
        return getCreationTime(b) - getCreationTime(a);

      case 'oldest-first':
        return getCreationTime(a) - getCreationTime(b);

      default:
        // Default fallback
        return getCreationTime(b) - getCreationTime(a);
    }
  });
};

/**
 * Get the default sort criteria
 */
export const getDefaultSortCriteria = (): SortCriteria => 'community-distance';

/**
 * Check if a sort criteria is available for the current book set
 */
export const isSortCriteriaAvailable = (books: Book[], criteria: SortCriteria): boolean => {
  switch (criteria) {
    case 'price-low-high':
    case 'price-high-low':
      return books.some(book => getEffectiveSellingPrice(book) !== null);
    
    case 'rental-low-high':
    case 'rental-high-low':
      return books.some(book => getEffectiveRentalPrice(book) !== null);
    
    case 'distance':
    case 'community-distance':
      return books.some(book => book.distance !== undefined);
    
    default:
      return true;
  }
};

/**
 * Get available sort options based on the current book set
 */
export const getAvailableSortOptions = (books: Book[]): SortOption[] => {
  return SORT_OPTIONS.filter(option => isSortCriteriaAvailable(books, option.value));
};
