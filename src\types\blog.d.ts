export interface BlogPost {
  id: string;
  title: string;
  content: string;
  tags: string[];
  published: boolean;
  createdAt: Date;
  updatedAt?: Date;
  authorId: string;
  authorName: string;
  featuredImage?: string;
  excerpt?: string;
}

export type BlogPostInput = {
  title: string;
  content: string;
  tags: string[];
  published: boolean;
  authorId: string;
  authorName: string;
  featuredImage?: string;
  excerpt?: string;
};

export type BlogPostUpdate = Partial<BlogPostInput> & {
  id: string;
};
