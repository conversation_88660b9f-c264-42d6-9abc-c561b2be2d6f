/**
 * LazyOwnerInformation Component
 *
 * Enhanced lazy-loaded wrapper for the Owner Information section
 * Optimized for LCP performance with immediate skeleton rendering
 * Uses React.lazy() and Suspense for performance optimization
 */

import React, { Suspense, useState, useEffect } from 'react';
import OwnerInformationSkeleton from './OwnerInformationSkeleton';
import { Book } from '@/types';
import { GeoCoordinates } from '@/lib/geolocationUtils';

// Lazy load the OwnerInformation component
const OwnerInformation = React.lazy(() => import('./OwnerInformation'));

interface LazyOwnerInformationProps {
  book: Book;
  distance: number | null;
  userLocation: GeoCoordinates | null;
  ownerPincode: string | null;
  locationPermission: 'granted' | 'denied' | 'unknown';
  onContactOwner: () => void;
  onRequestLocation: () => void;
  /** Show skeleton immediately for LCP optimization */
  showImmediateSkeleton?: boolean;
  /** Enhanced skeleton with more details */
  enhancedSkeleton?: boolean;
}

const LazyOwnerInformation: React.FC<LazyOwnerInformationProps> = ({
  showImmediateSkeleton = true,
  enhancedSkeleton = false,
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(showImmediateSkeleton);

  useEffect(() => {
    if (showImmediateSkeleton) {
      // Show skeleton immediately, then load component
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 100); // Small delay to ensure skeleton is visible

      return () => clearTimeout(timer);
    }
  }, [showImmediateSkeleton]);

  // If immediate skeleton is enabled and we're still loading, show skeleton
  if (showImmediateSkeleton && isLoading) {
    return <OwnerInformationSkeleton enhanced={enhancedSkeleton} />;
  }

  return (
    <Suspense
      fallback={
        <OwnerInformationSkeleton
          enhanced={enhancedSkeleton}
          className="transition-opacity duration-300"
        />
      }
    >
      <div className="transition-opacity duration-300">
        <OwnerInformation {...props} />
      </div>
    </Suspense>
  );
};

export default LazyOwnerInformation;
