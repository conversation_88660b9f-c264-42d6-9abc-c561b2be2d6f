
import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button-variants";
import { BookOpen } from "lucide-react";

const NotFound = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
      <div className="text-center max-w-md">
        <div className="flex justify-center mb-6">
          <div className="bg-burgundy-100 p-4 rounded-full">
            <BookOpen className="h-12 w-12 text-burgundy-500" />
          </div>
        </div>
        
        <h1 className="text-4xl font-bold font-playfair text-navy-800 mb-2">
          Page Not Found
        </h1>
        
        <p className="text-lg text-gray-600 mb-6">
          The page you're looking for doesn't seem to exist. It may have been moved or deleted.
        </p>
        
        <div className="space-y-4">
          <Link to="/">
            <Button className="w-full">
              Return to Home
            </Button>
          </Link>
          
          <p className="text-gray-500 text-sm">
            Lost? Try searching for books or browsing our categories.
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
