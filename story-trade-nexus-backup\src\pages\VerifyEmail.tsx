import { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import { Mail, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button-variants";
import { useAuth } from "@/lib/AuthContext";
import { toast } from "sonner";
import MainLayout from "@/components/layouts/MainLayout";

const VerifyEmail = () => {
  const { currentUser, emailVerified, sendVerificationEmail, reloadUser, signOut } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const navigate = useNavigate();

  // Check if email is verified periodically
  useEffect(() => {
    if (!currentUser) {
      navigate("/signin");
      return;
    }

    if (emailVerified) {
      navigate("/");
      return;
    }

    // Check email verification status every 5 seconds
    const interval = setInterval(async () => {
      try {
        const isVerified = await reloadUser();
        if (isVerified) {
          toast.success("Email verified successfully!");
          navigate("/");
        }
      } catch (error) {
        console.error("Error checking verification status:", error);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [currentUser, emailVerified, navigate, reloadUser]);

  // Handle countdown for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && resendDisabled) {
      setResendDisabled(false);
    }
  }, [countdown, resendDisabled]);

  // Handle resend verification email
  const handleResendEmail = async () => {
    if (resendDisabled) return;

    setIsLoading(true);
    try {
      await sendVerificationEmail();
      toast.success("Verification email sent! Please check your inbox.");
      setResendDisabled(true);
      setCountdown(60); // Disable resend for 60 seconds
    } catch (error) {
      console.error("Error sending verification email:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to send verification email";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle manual verification check
  const handleCheckVerification = async () => {
    setIsLoading(true);
    try {
      const isVerified = await reloadUser();
      if (isVerified) {
        toast.success("Email verified successfully! Redirecting to dashboard...");
        setTimeout(() => navigate("/dashboard"), 1500);
      } else {
        toast.info("Your email is not verified yet. Please check your inbox and click the verification link.");
      }
    } catch (error) {
      console.error("Error checking verification status:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to check verification status";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
      navigate("/signin");
    } catch (error) {
      console.error("Error signing out:", error);
      toast.error("Failed to sign out");
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8 max-w-md">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center">
            <div className="mx-auto bg-blue-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4">
              <Mail className="h-8 w-8 text-blue-600" />
            </div>
            <h1 className="text-2xl font-bold text-navy-800 font-playfair mb-2">Verify Your Email</h1>
            <p className="text-gray-600 mb-4">
              We've sent a verification email to <strong>{currentUser?.email}</strong>.
              Please check your inbox and click the verification link to activate your account.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
              <p className="text-blue-800 text-sm">
                <strong>Important:</strong> You must verify your email before you can access all features.
                This helps ensure the security of your account and allows us to contact you about your books.
              </p>
              <p className="text-blue-700 text-sm mt-2">
                <strong>Note:</strong> We're automatically checking your verification status every few seconds.
                Once you click the verification link in your email, you'll be redirected automatically.
              </p>
              <p className="text-blue-700 text-sm mt-2">
                <strong>While waiting for verification:</strong> You can still browse books, view book details,
                and access public pages, but you won't be able to add books or access your dashboard until
                your email is verified.
              </p>
            </div>

            <div className="space-y-4">
              <Button
                onClick={handleResendEmail}
                disabled={isLoading || resendDisabled}
                className="w-full flex items-center justify-center gap-2"
              >
                {isLoading && resendDisabled ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Mail className="h-4 w-4" />
                )}
                {resendDisabled
                  ? `Resend Email (${countdown}s)`
                  : isLoading && !resendDisabled
                  ? "Sending..."
                  : "Resend Verification Email"}
              </Button>

              <Button
                onClick={handleCheckVerification}
                variant="secondary"
                disabled={isLoading}
                className="w-full flex items-center justify-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${isLoading && !resendDisabled ? 'animate-spin' : ''}`} />
                {isLoading && !resendDisabled ? "Checking..." : "I've Verified My Email"}
              </Button>

              <Link to="/">
                <Button
                  variant="secondary"
                  className="w-full mb-2"
                >
                  Continue Browsing Books
                </Button>
              </Link>

              <Button
                variant="outline"
                onClick={handleSignOut}
                className="w-full"
              >
                Sign Out
              </Button>
            </div>

            <div className="mt-6 text-sm text-gray-500">
              <p>
                Didn't receive the email? Check your spam folder or{" "}
                <button
                  onClick={handleResendEmail}
                  disabled={resendDisabled}
                  className="text-burgundy-500 hover:underline"
                >
                  click here to resend
                </button>
                .
              </p>
            </div>

            <div className="mt-6 border-t border-gray-200 pt-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Troubleshooting Tips:</h3>
              <ul className="text-xs text-gray-600 space-y-1 list-disc pl-5">
                <li>Check your spam or junk folder</li>
                <li>Add <span className="font-mono"><EMAIL></span> to your contacts</li>
                <li>If using Gmail, check the "Promotions" or "Updates" tabs</li>
                <li>Try using a different browser or device to verify</li>
                <li>If you're still having issues, contact support at <a href="mailto:<EMAIL>" className="text-burgundy-500 hover:underline"><EMAIL></a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default VerifyEmail;
