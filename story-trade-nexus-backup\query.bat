@echo off
REM Firebase Community Query Tool - Windows Batch Script
REM Usage: query.bat <pincode>
REM Example: query.bat 500001

if "%1"=="" (
    echo.
    echo 🏘️  Firebase Community Query Tool
    echo 📍 Searching Hyderabad Properties Database
    echo.
    echo ❌ Error: Pincode argument is required
    echo.
    echo 📋 Usage: query.bat ^<pincode^>
    echo 📝 Example: query.bat 500001
    echo.
    pause
    exit /b 1
)

echo.
echo 🚀 Starting Firebase Community Query...
echo.

node queryCommunities.js %1

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Query failed with error code %ERRORLEVEL%
    echo.
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ✅ Query completed successfully!
echo.
pause
