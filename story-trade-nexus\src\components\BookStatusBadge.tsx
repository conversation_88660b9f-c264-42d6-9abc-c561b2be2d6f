import React from 'react';
import { Clock, CheckCircle, XCircle, Calendar } from 'lucide-react';
import { BookStatus } from '@/types';

interface BookStatusBadgeProps {
  status?: BookStatus;
  nextAvailableDate?: Date;
  className?: string;
}

export const BookStatusBadge: React.FC<BookStatusBadgeProps> = ({
  status = 'Available',
  nextAvailableDate,
  className = ''
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'Available':
        return {
          icon: CheckCircle,
          text: 'Available',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          iconColor: 'text-green-600'
        };
      case 'Sold Out':
        return {
          icon: XCircle,
          text: 'Sold Out',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          iconColor: 'text-red-600'
        };
      case 'Rented Out':
        return {
          icon: Clock,
          text: nextAvailableDate 
            ? `Rented (back ${nextAvailableDate.toLocaleDateString()})`
            : 'Rented Out',
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-600'
        };
      default:
        return {
          icon: CheckCircle,
          text: 'Available',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          iconColor: 'text-green-600'
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor} ${className}`}>
      <Icon className={`h-3 w-3 mr-1 ${config.iconColor}`} />
      {config.text}
    </div>
  );
};
