import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { initializeFirebase } from './lib/firebase'; // Import Firebase initialization function

// Initialize Firebase before rendering the app
const initializeApp = async () => {
  try {
    console.log('Initializing Firebase...');
    await initializeFirebase();
    console.log('Firebase initialized successfully');

    // Initialize the app with strict mode disabled for now
    const root = createRoot(document.getElementById("root")!);
    root.render(<App />);
  } catch (error) {
    console.error('Failed to initialize Firebase:', error);

    // Render the app anyway to show error messages
    const root = createRoot(document.getElementById("root")!);
    root.render(<App />);
  }
};

initializeApp();
