import React, { useState, ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { BookCheck, Users, Settings, BarChart3, LogOut, Home, Menu, X, MessageSquare, Star, FileText } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/AuthContext';
import { cn } from '@/lib/utils';

interface AdminLayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({
  children,
  title = "Admin Dashboard",
  description = "Manage your book-sharing platform from here."
}) => {
  const { currentUser, signOut } = useAuth();
  const location = useLocation();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const adminNavItems = [
    {
      title: 'Dashboard',
      icon: <Home className="h-5 w-5" />,
      link: '/admin',
      description: 'Admin dashboard overview'
    },
    {
      title: 'Book Approvals',
      icon: <BookCheck className="h-5 w-5" />,
      link: '/admin/books',
      description: 'Review and approve new book submissions'
    },
    {
      title: 'User Management',
      icon: <Users className="h-5 w-5" />,
      link: '/admin/users',
      description: 'Manage users and permissions'
    },
    {
      title: 'Blog Manager',
      icon: <FileText className="h-5 w-5" />,
      link: '/admin/blogs',
      description: 'Create and manage blog posts and content'
    },
    {
      title: 'Contact Messages',
      icon: <MessageSquare className="h-5 w-5" />,
      link: '/admin/messages',
      description: 'View and manage contact messages from users'
    },
    {
      title: 'Feedback',
      icon: <Star className="h-5 w-5" />,
      link: '/admin/feedback',
      description: 'View and manage user feedback and support requests'
    },
    {
      title: 'Admin Tools',
      icon: <Settings className="h-5 w-5" />,
      link: '/admin/utilities',
      description: 'Administrative utilities and functions'
    },
    {
      title: 'Admin Settings',
      icon: <BarChart3 className="h-5 w-5" />,
      link: '/admin/settings',
      description: 'Configure admin preferences and system settings'
    }
  ];

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow flex flex-col md:flex-row">
        {/* Mobile sidebar toggle */}
        <div className="md:hidden p-4 bg-white border-b">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
            className="ml-auto flex"
          >
            {isMobileSidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            <span className="sr-only">Toggle menu</span>
          </Button>
        </div>

        {/* Sidebar - Desktop (always visible) and Mobile (toggleable) */}
        <aside
          className={cn(
            "w-full md:w-64 bg-white shadow-md md:shadow-none transition-all duration-300 ease-in-out",
            "md:block", // Always visible on desktop
            isMobileSidebarOpen ? "block" : "hidden" // Toggle on mobile
          )}
        >
          <div className="p-6 border-b">
            <h2 className="text-xl font-bold text-navy-800 mb-2">Admin Panel</h2>
            <p className="text-sm text-gray-500">
              Welcome, {currentUser?.displayName || currentUser?.email?.split('@')[0] || 'Admin'}
            </p>
          </div>

          <nav className="p-4 space-y-1">
            {adminNavItems.map((item) => (
              <Link
                key={item.title}
                to={item.link}
                className={cn(
                  "flex items-center px-4 py-3 rounded-md transition-colors",
                  isActive(item.link)
                    ? "bg-burgundy-50 text-burgundy-700 font-medium"
                    : "text-gray-700 hover:bg-gray-100"
                )}
                title={item.description}
              >
                <span className="mr-3">{item.icon}</span>
                <span>{item.title}</span>
              </Link>
            ))}

            <button
              onClick={handleSignOut}
              className="w-full flex items-center px-4 py-3 rounded-md text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              <span>Sign Out</span>
            </button>
          </nav>
        </aside>

        {/* Main content area */}
        <div className="flex-1 p-4 md:p-8 bg-gray-50">
          <div className="max-w-5xl mx-auto">
            {children}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default AdminLayout;
