import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Check, X, Refresh<PERSON><PERSON>, Eye, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import { Book } from '@/types';
import { getPendingBooks, approveBook, rejectBook } from '@/lib/bookService';
import { createTestPendingBook } from '@/lib/adminUtils';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from '@/components/ui/textarea';
import AdminLayout from '@/components/layout/AdminLayout';

const AdminBookApprovals: React.FC = () => {
  const [pendingBooks, setPendingBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedBook, setSelectedBook] = useState<Book | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isRejectionDialogOpen, setIsRejectionDialogOpen] = useState(false);
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [isCreatingTestBook, setIsCreatingTestBook] = useState(false);

  useEffect(() => {
    fetchPendingBooks();
  }, []);

  const fetchPendingBooks = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching pending books...');
      const books = await getPendingBooks();
      console.log(`Received ${books.length} pending books`);
      setPendingBooks(books);
    } catch (error) {
      console.error('Error fetching pending books:', error);

      // Display a more specific error message if available
      if (error instanceof Error) {
        setError(`Failed to load pending books: ${error.message}`);
      } else {
        setError('Failed to load pending books. Please try again.');
      }

      // Log additional details for debugging
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleApproveBook = async (book: Book) => {
    try {
      setIsApproving(true);
      await approveBook(book.id);
      toast.success(`"${book.title}" has been approved`);
      setPendingBooks(pendingBooks.filter(b => b.id !== book.id));
    } catch (error) {
      console.error('Error approving book:', error);
      toast.error('Failed to approve book. Please try again.');
    } finally {
      setIsApproving(false);
    }
  };

  const openRejectDialog = (book: Book) => {
    setSelectedBook(book);
    setRejectionReason('');
    setIsRejectionDialogOpen(true);
  };

  const handleRejectBook = async () => {
    if (!selectedBook) return;

    try {
      setIsRejecting(true);
      await rejectBook(selectedBook.id, rejectionReason);
      toast.success(`"${selectedBook.title}" has been rejected`);
      setPendingBooks(pendingBooks.filter(b => b.id !== selectedBook.id));
      setIsRejectionDialogOpen(false);
    } catch (error) {
      console.error('Error rejecting book:', error);
      toast.error('Failed to reject book. Please try again.');
    } finally {
      setIsRejecting(false);
    }
  };

  const handleCreateTestBook = async () => {
    try {
      setIsCreatingTestBook(true);
      const bookId = await createTestPendingBook();
      toast.success('Test pending book created successfully');
      fetchPendingBooks(); // Refresh the list to show the new book
    } catch (error) {
      console.error('Error creating test book:', error);
      toast.error('Failed to create test book. Please try again.');
    } finally {
      setIsCreatingTestBook(false);
    }
  };

  return (
    <AdminLayout title="Book Approvals" description="Review and approve new book submissions">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-navy-800 mb-2">Book Approvals</h1>
          <p className="text-gray-600">Review and approve new book submissions</p>
        </div>

        <div className="flex gap-2 mt-4 md:mt-0">
          <Button
            variant="outline"
            onClick={handleCreateTestBook}
            disabled={isCreatingTestBook || loading}
            className="flex items-center"
          >
            {isCreatingTestBook ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Creating...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Create Test Book
              </>
            )}
          </Button>

          <Button
            variant="outline"
            onClick={fetchPendingBooks}
            disabled={loading}
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </>
            )}
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
          <details className="mt-2">
            <summary className="text-sm cursor-pointer">Debug Information</summary>
            <div className="mt-2 text-xs">
              <p>If you're seeing this error, try the following:</p>
              <ol className="list-decimal pl-5 mt-1 space-y-1">
                <li>Click the "Create Test Book" button to add a test pending book</li>
                <li>Check your Firebase permissions and rules</li>
                <li>Verify that your Firestore database has the correct structure</li>
                <li>Check the browser console for more detailed error messages</li>
              </ol>
            </div>
          </details>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Spinner size="lg" />
          <span className="ml-2 text-gray-600">Loading pending books...</span>
        </div>
      ) : pendingBooks.length > 0 ? (
        <div className="space-y-6">
          {pendingBooks.map((book) => (
            <div key={book.id} className="bg-white rounded-lg shadow-md p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="w-32 h-32 flex-shrink-0">
                  <img
                    src={book.imageUrl || 'https://via.placeholder.com/150?text=No+Image'}
                    alt={book.title}
                    className="w-full h-full object-cover rounded-md"
                  />
                </div>
                <div className="flex-1">
                  <h2 className="text-xl font-medium text-navy-800">{book.title}</h2>
                  <p className="text-gray-600 mb-2">by {book.author}</p>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 mb-2">
                    <div>
                      <span className="text-sm text-gray-500">Owner:</span> {book.ownerName}
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Genre:</span> {Array.isArray(book.genre) ? book.genre.join(', ') : book.genre}
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Condition:</span> {book.condition}
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Availability:</span> {book.availability}
                    </div>
                  </div>

                  <p className="text-sm text-gray-700 mb-3 line-clamp-2">{book.description}</p>

                  <div className="flex flex-wrap gap-2">
                    <Link to={`/books/${book.id}`} target="_blank" rel="noopener noreferrer">
                      <Button variant="outline" size="sm" className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        View Details
                      </Button>
                    </Link>

                    <Button
                      variant="default"
                      size="sm"
                      className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                      onClick={() => handleApproveBook(book)}
                      disabled={isApproving}
                    >
                      {isApproving ? (
                        <Spinner size="sm" className="mr-1" />
                      ) : (
                        <Check className="h-4 w-4" />
                      )}
                      Approve
                    </Button>

                    <Button
                      variant="destructive"
                      size="sm"
                      className="flex items-center gap-1"
                      onClick={() => openRejectDialog(book)}
                      disabled={isRejecting}
                    >
                      <X className="h-4 w-4" />
                      Reject
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <h2 className="text-xl font-medium text-gray-700 mb-2">No pending books</h2>
          <p className="text-gray-500 mb-4">All books have been reviewed. Check back later for new submissions.</p>
          <Button
            variant="outline"
            onClick={handleCreateTestBook}
            disabled={isCreatingTestBook}
            className="mt-2"
          >
            {isCreatingTestBook ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Creating Test Book...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Create Test Book for Approval
              </>
            )}
          </Button>
          <p className="text-xs text-gray-500 mt-4">
            Click the button above to create a test book with pending approval status.
            This is useful for testing the approval workflow.
          </p>
        </div>
      )}
      {/* Rejection Dialog */}
      <Dialog open={isRejectionDialogOpen} onOpenChange={setIsRejectionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Book</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting "{selectedBook?.title}". This will be visible to the user who submitted the book.
            </DialogDescription>
          </DialogHeader>

          <Textarea
            placeholder="Enter rejection reason..."
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            className="min-h-[100px]"
          />

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRejectionDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleRejectBook}
              disabled={!rejectionReason.trim() || isRejecting}
            >
              {isRejecting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Rejecting...
                </>
              ) : (
                'Confirm Rejection'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminBookApprovals;
