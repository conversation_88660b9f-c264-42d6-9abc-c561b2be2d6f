# Properties Update Script

This TypeScript script updates the existing Firebase Firestore collection 'hyderabadProperties' with new property data from configurable CSV files.

## Features

✅ **Data Validation**: Validates CSV data integrity before processing
✅ **Duplicate Prevention**: Checks for existing records to prevent duplicates
✅ **Batch Processing**: Efficiently processes data in configurable batches
✅ **Error Handling**: Comprehensive error handling with detailed logging
✅ **Dry Run Mode**: Preview changes without updating the database
✅ **Progress Reporting**: Real-time progress updates and summary statistics
✅ **TypeScript Support**: Full TypeScript type safety and interfaces
✅ **ES Modules**: Compatible with project's ES module configuration

## Prerequisites

- Node.js (v14 or higher)
- TypeScript configured in the project
- Firebase project with Firestore enabled
- CSV file with property data (default: `hyderabad_properties.csv` in project root)

## CSV File Requirements

The CSV file can be located anywhere (specify path with `--csv-file` option) and must contain the following columns:

| Column | Type | Description | Validation |
|--------|------|-------------|------------|
| `communityname` | string | Name of the property/community | Required, max 200 chars |
| `address` | string | Full address of the property | Required |
| `pincode` | string | 6-digit Indian postal code | Required, must be 6 digits |
| `latitude` | string | Latitude coordinate | Required, -90 to 90 |
| `longitude` | string | Longitude coordinate | Required, -180 to 180 |

### Example CSV Format:
```csv
communityname,address,pincode,latitude,longitude
"Nestcon's Chintala Residency - Alwal","Survey No.16, 1-6-46 & 47, Panta Reddy Colony, Tirumala Enclave, Alwal, Secunderabad, Telangana 500010, India",500010,17.5017236,78.4992728
```

## Usage

### Basic Commands

```bash
# Dry run with default CSV file (preview changes without updating database)
npm run update:properties:dry

# Live run with default CSV file (actual database update)
npm run update:properties

# Dry run with custom CSV file
npm run update:properties:dry -- --csv-file=my-data.csv

# Live run with custom CSV file and batch size
npm run update:properties -- --csv-file=my-data.csv --batch-size=250

# Display help
npm run update:properties -- --help
```

### Alternative Execution Methods

```bash
# Using tsx directly with default CSV
npx tsx src/scripts/updatePropertiesFromCSV.ts --dry-run

# Using tsx with custom CSV file
npx tsx src/scripts/updatePropertiesFromCSV.ts --csv-file=my-data.csv --dry-run
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--csv-file=<path>` | Path to CSV file | hyderabad_properties.csv |
| `--input=<path>` | Alternative syntax for CSV file path | - |
| `--dry-run` | Preview changes without updating database | false |
| `--batch-size=<number>` | Set custom batch size (max 500) | 500 |
| `--help` | Display help information | - |

## Data Processing Strategy

### 1. Merge Strategy
- **New Records**: Added to the collection with auto-generated IDs
- **Existing Records**: Skipped to prevent duplicates
- **Duplicate Detection**: Based on `communityNameLower` + `pincode` combination

### 2. Document Structure
Each document in the `hyderabadProperties` collection contains:

```typescript
{
  communityName: string;           // Original community name
  address: string;                 // Full address
  pincode: number | string;        // Postal code (number if valid, string otherwise)
  location: {                      // Coordinate object
    latitude: number;
    longitude: number;
  };
  geoPoint: {                      // GeoPoint for Firestore geoqueries
    latitude: number;
    longitude: number;
  };
  createdAt: Date;                 // Creation timestamp
  updatedAt: Date;                 // Last update timestamp
  communityNameLower: string;      // Lowercase name for case-insensitive queries
  imported: boolean;               // Flag indicating imported record
  source: string;                  // Source CSV filename
  batchId: string;                 // Unique batch identifier
}
```

## Error Handling

The script handles various error scenarios:

- **File Not Found**: CSV file missing from expected location
- **Invalid Data**: Malformed CSV rows or invalid data types
- **Network Issues**: Firebase connection problems
- **Firestore Limits**: Batch size and rate limiting
- **Duplicate Detection**: Existing record conflicts

## Logging

The script provides detailed logging with timestamps:

- `[INFO]`: General information and progress updates
- `[SUCCESS]`: Successful operations
- `[WARNING]`: Non-critical issues (validation errors, duplicates)
- `[ERROR]`: Critical errors that stop processing
- `[DEBUG]`: Detailed debugging information

## Output Example

```
[INFO] 2024-01-15T10:30:00.000Z: Starting Hyderabad properties update process...
[INFO] 2024-01-15T10:30:00.100Z: 🔍 Running in DRY RUN mode - no actual database changes will be made
[INFO] 2024-01-15T10:30:00.200Z: Using batch size: 500
[SUCCESS] 2024-01-15T10:30:01.000Z: Firebase initialized successfully
[SUCCESS] 2024-01-15T10:30:02.000Z: Successfully read 1708 records from CSV
[INFO] 2024-01-15T10:30:02.100Z: Validating CSV records...
[INFO] 2024-01-15T10:30:03.000Z: Validation complete: 1705 valid, 3 invalid records
[INFO] 2024-01-15T10:30:03.100Z: Transforming records to Firestore documents...
[INFO] 2024-01-15T10:30:03.200Z: Processing 1705 documents in batches of 500...

===== Update Summary =====
[SUCCESS] 2024-01-15T10:30:10.000Z: Total CSV records read: 1708
[SUCCESS] 2024-01-15T10:30:10.001Z: Valid records: 1705
[SUCCESS] 2024-01-15T10:30:10.002Z: Records skipped (validation errors): 3
[INFO] 2024-01-15T10:30:10.003Z: [DRY RUN] Would add 1650 new records to Firestore
[SUCCESS] 2024-01-15T10:30:10.004Z: No errors encountered during processing
[SUCCESS] 2024-01-15T10:30:10.005Z: Processing completed in 8.5 seconds
```

## Troubleshooting

### Common Issues

1. **CSV File Not Found**
   ```
   Error: CSV file not found: /path/to/hyderabad_properties_500010-500040.csv
   ```
   **Solution**: Ensure the CSV file is in the project root directory

2. **Firebase Connection Error**
   ```
   Error: Firebase initialization failed
   ```
   **Solution**: Check Firebase configuration in `src/lib/firebase.ts`

3. **Invalid Coordinates**
   ```
   Row 123: Invalid latitude '91.5' (must be between -90 and 90)
   ```
   **Solution**: Fix coordinate values in the CSV file

4. **TypeScript Compilation Error**
   ```
   Error: Cannot find module 'csv-parse'
   ```
   **Solution**: Install missing dependencies:
   ```bash
   npm install csv-parse
   npm install --save-dev @types/node
   ```

### Performance Considerations

- **Batch Size**: Default 500 is optimal for Firestore limits
- **Rate Limiting**: 1-second delay between batches prevents API throttling
- **Memory Usage**: Large CSV files are processed in streams to minimize memory usage
- **Network**: Ensure stable internet connection for Firebase operations

## Integration with Existing Scripts

This script follows the same patterns as existing scripts in the project:

- Uses the same Firebase configuration from `src/lib/firebase.ts`
- Follows the document structure from `importHyderabadProperties.js`
- Compatible with the community utilities in `src/lib/communityUtils.ts`
- Integrates with the pincode location services

## Security Considerations

- **Dry Run First**: Always run with `--dry-run` before live execution
- **Data Validation**: All input data is validated before processing
- **Error Isolation**: Individual record errors don't stop the entire process
- **Audit Trail**: Each record includes source and batch information for tracking

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the script logs for specific error messages
3. Ensure all prerequisites are met
4. Verify CSV file format and location
