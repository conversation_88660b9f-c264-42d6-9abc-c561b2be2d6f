import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ChevronLeft, ChevronRight, ZoomIn, Search } from 'lucide-react';
import { LazyImage } from '@/components/LazyImage';

interface ImageCarouselProps {
  images: string[];
  initialIndex?: number;
  alt: string;
  className?: string;
  maxZoomLevel?: number;
  containerHeight?: string;
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({
  images,
  initialIndex = 0,
  alt,
  className = '',
  maxZoomLevel = 2.5,
  containerHeight = '400px',
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [loadedImages, setLoadedImages] = useState<Record<number, boolean>>({});
  const [preloadedImages, setPreloadedImages] = useState<Record<number, boolean>>({});
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(maxZoomLevel);
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });
  const [showZoomIndicator, setShowZoomIndicator] = useState(true);
  const [showMagnifier, setShowMagnifier] = useState(false);
  const [magnifierPosition, setMagnifierPosition] = useState({ x: 0, y: 0 });
  const [magnifierSize, setMagnifierSize] = useState(150); // Size of the magnifier in pixels

  // Refs
  const transitionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const zoomIndicatorTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const mouseMoveThrottleRef = useRef<NodeJS.Timeout | null>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const imageRefs = useRef<(HTMLImageElement | null)[]>([]);
  const rafRef = useRef<number | null>(null); // For requestAnimationFrame

  // Initialize image refs array
  useEffect(() => {
    imageRefs.current = Array(images.length).fill(null);
  }, [images.length]);

  // Preload adjacent images
  useEffect(() => {
    const preloadImage = (index: number) => {
      if (index >= 0 && index < images.length && !preloadedImages[index]) {
        const img = new Image();
        img.src = images[index];
        img.onload = () => {
          setPreloadedImages(prev => ({ ...prev, [index]: true }));
        };
      }
    };

    // Preload current image and adjacent images
    preloadImage(currentIndex);
    preloadImage(currentIndex - 1 < 0 ? images.length - 1 : currentIndex - 1);
    preloadImage(currentIndex + 1 >= images.length ? 0 : currentIndex + 1);
  }, [currentIndex, images, preloadedImages]);

  // Handle image load
  const handleImageLoad = (index: number) => {
    setLoadedImages(prev => ({ ...prev, [index]: true }));
  };

  // Navigate to previous image with transition
  const prevImage = () => {
    if (isTransitioning || images.length <= 1) return;

    setIsTransitioning(true);
    const newIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;

    // Clear any existing timeout
    if (transitionTimeoutRef.current) {
      clearTimeout(transitionTimeoutRef.current);
    }

    // Set a timeout to match the CSS transition duration
    transitionTimeoutRef.current = setTimeout(() => {
      setCurrentIndex(newIndex);
      setIsTransitioning(false);
    }, 150); // Match this with the CSS transition duration
  };

  // Navigate to next image with transition
  const nextImage = () => {
    if (isTransitioning || images.length <= 1) return;

    setIsTransitioning(true);
    const newIndex = currentIndex === images.length - 1 ? 0 : currentIndex + 1;

    // Clear any existing timeout
    if (transitionTimeoutRef.current) {
      clearTimeout(transitionTimeoutRef.current);
    }

    // Set a timeout to match the CSS transition duration
    transitionTimeoutRef.current = setTimeout(() => {
      setCurrentIndex(newIndex);
      setIsTransitioning(false);
    }, 150); // Match this with the CSS transition duration
  };

  // Optimized mouse move handler with requestAnimationFrame for smoother performance
  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!imageContainerRef.current) return;

    // Cancel any existing animation frame to prevent queuing
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }

    // Use requestAnimationFrame for smoother updates
    rafRef.current = requestAnimationFrame(() => {
      const { left, top, width, height } = imageContainerRef.current!.getBoundingClientRect();

      // Calculate mouse position relative to the container (0 to 1)
      const x = Math.max(0, Math.min(1, (e.clientX - left) / width));
      const y = Math.max(0, Math.min(1, (e.clientY - top) / height));

      // Update mouse position for zoom effect
      setMousePosition({ x, y });

      // Update magnifier position for magnifying glass effect
      setMagnifierPosition({
        x: e.clientX - left - magnifierSize / 2,
        y: e.clientY - top - magnifierSize / 2
      });
    });
  }, [magnifierSize]);

  // Handle mouse enter - show zoom indicator briefly
  const handleMouseEnter = useCallback(() => {
    setShowZoomIndicator(true);

    // Clear any existing timeout
    if (zoomIndicatorTimeoutRef.current) {
      clearTimeout(zoomIndicatorTimeoutRef.current);
    }

    // Hide the indicator after 1.5 seconds
    zoomIndicatorTimeoutRef.current = setTimeout(() => {
      setShowZoomIndicator(false);
    }, 1500);
  }, []);

  // Handle mouse leave - reset zoom and clean up
  const handleMouseLeave = useCallback(() => {
    setIsZoomed(false);
    setShowMagnifier(false);
    setShowZoomIndicator(false);

    // Clear the timeout
    if (zoomIndicatorTimeoutRef.current) {
      clearTimeout(zoomIndicatorTimeoutRef.current);
    }

    // Cancel any animation frame
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
      rafRef.current = null;
    }
  }, []);

  // Toggle between zoom modes (transform zoom or magnifier)
  const toggleZoom = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    // Only toggle if not clicking on navigation buttons
    if (e.target === e.currentTarget || (e.target as HTMLElement).tagName === 'IMG') {
      if (!isZoomed) {
        // First click: activate transform zoom
        setIsZoomed(true);
        setShowMagnifier(false);
      } else if (!showMagnifier) {
        // Second click: switch to magnifier mode
        setShowMagnifier(true);
      } else {
        // Third click: turn off zoom completely
        setIsZoomed(false);
        setShowMagnifier(false);
      }
    }
  }, [isZoomed, showMagnifier]);

  // Clean up timeouts and animation frames on unmount
  useEffect(() => {
    return () => {
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current);
      }
      if (zoomIndicatorTimeoutRef.current) {
        clearTimeout(zoomIndicatorTimeoutRef.current);
      }
      if (mouseMoveThrottleRef.current) {
        clearTimeout(mouseMoveThrottleRef.current);
      }
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, []);

  // If there are no images, return null
  if (!images || images.length === 0) {
    return null;
  }

  // If there's only one image, render it without navigation but with enhanced zoom
  if (images.length === 1) {
    return (
      <div className={`relative w-full ${className}`} style={{ height: containerHeight }}>
        <div
          ref={imageContainerRef}
          className="overflow-hidden h-full w-full flex items-center justify-center p-4 bg-white relative cursor-zoom-in"
          onMouseMove={handleMouseMove}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onClick={toggleZoom}
        >
          <LazyImage
            src={images[0]}
            alt={`${alt}`}
            className="w-full h-full object-contain drop-shadow-sm transition-all duration-300 ease-out"
            style={{
              objectFit: "contain",
              maxHeight: "100%",
              maxWidth: "100%",
              transform: isZoomed && !showMagnifier
                ? `scale(${zoomLevel}) translate(${(0.5 - mousePosition.x) * -100}%, ${(0.5 - mousePosition.y) * -100}%)`
                : 'scale(1)',
              transformOrigin: isZoomed ? `${mousePosition.x * 100}% ${mousePosition.y * 100}%` : 'center center',
              filter: showMagnifier ? 'brightness(0.9)' : 'none'
            }}
            onLoad={() => handleImageLoad(0)}
            loading="eager"
          />

          {/* Magnifying glass effect */}
          {showMagnifier && (
            <div
              className="absolute rounded-full overflow-hidden border-2 border-white shadow-lg pointer-events-none z-30"
              style={{
                width: `${magnifierSize}px`,
                height: `${magnifierSize}px`,
                left: `${magnifierPosition.x}px`,
                top: `${magnifierPosition.y}px`,
                backgroundImage: `url(${images[0]})`,
                backgroundPosition: `calc(${mousePosition.x * 100}% + ${magnifierSize / 2}px - ${mousePosition.x * magnifierSize}px) calc(${mousePosition.y * 100}% + ${magnifierSize / 2}px - ${mousePosition.y * magnifierSize}px)`,
                backgroundSize: `${zoomLevel * 100}%`,
                backgroundRepeat: 'no-repeat'
              }}
            />
          )}

          {/* Zoom indicator */}
          {showZoomIndicator && (
            <div className="absolute top-2 right-2 bg-black/70 text-white rounded-full p-2 transition-opacity duration-300 flex items-center gap-1.5 z-20">
              {!isZoomed ? (
                <>
                  <ZoomIn className="h-4 w-4" />
                  <span className="text-xs">Click to zoom</span>
                </>
              ) : !showMagnifier ? (
                <>
                  <Search className="h-4 w-4" />
                  <span className="text-xs">Click for magnifier</span>
                </>
              ) : (
                <>
                  <ZoomIn className="h-4 w-4 rotate-180" />
                  <span className="text-xs">Click to reset</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`relative w-full ${className}`} style={{ height: containerHeight }}>
      <div className="relative h-full w-full flex items-center justify-center overflow-hidden">
        <div
          ref={imageContainerRef}
          className={`overflow-hidden h-full w-full flex items-center justify-center p-4 bg-white transition-opacity duration-150 ease-in-out relative cursor-zoom-in ${isTransitioning ? 'opacity-70' : 'opacity-100'}`}
          onMouseMove={handleMouseMove}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onClick={toggleZoom}
        >
          <LazyImage
            key={`image-${currentIndex}`}
            src={images[currentIndex]}
            alt={`${alt} - Image ${currentIndex + 1}`}
            className="w-full h-full object-contain drop-shadow-sm transition-all duration-300 ease-out"
            style={{
              objectFit: "contain",
              maxHeight: "100%",
              maxWidth: "100%",
              transform: isZoomed && !showMagnifier
                ? `scale(${zoomLevel}) translate(${(0.5 - mousePosition.x) * -100}%, ${(0.5 - mousePosition.y) * -100}%)`
                : isTransitioning ? 'scale(0.95)' : 'scale(1)',
              transformOrigin: isZoomed ? `${mousePosition.x * 100}% ${mousePosition.y * 100}%` : 'center center',
              pointerEvents: isZoomed ? 'none' : 'auto', // Prevent image from capturing clicks when zoomed
              filter: showMagnifier ? 'brightness(0.9)' : 'none'
            }}
            onLoad={() => handleImageLoad(currentIndex)}
            loading={currentIndex === 0 ? "eager" : "lazy"}
          />

          {/* Magnifying glass effect */}
          {showMagnifier && (
            <div
              className="absolute rounded-full overflow-hidden border-2 border-white shadow-lg pointer-events-none z-30"
              style={{
                width: `${magnifierSize}px`,
                height: `${magnifierSize}px`,
                left: `${magnifierPosition.x}px`,
                top: `${magnifierPosition.y}px`,
                backgroundImage: `url(${images[currentIndex]})`,
                backgroundPosition: `calc(${mousePosition.x * 100}% + ${magnifierSize / 2}px - ${mousePosition.x * magnifierSize}px) calc(${mousePosition.y * 100}% + ${magnifierSize / 2}px - ${mousePosition.y * magnifierSize}px)`,
                backgroundSize: `${zoomLevel * 100}%`,
                backgroundRepeat: 'no-repeat'
              }}
            />
          )}

          {/* Zoom indicator */}
          {showZoomIndicator && (
            <div className="absolute top-2 right-2 bg-black/70 text-white rounded-full p-2 transition-opacity duration-300 flex items-center gap-1.5 z-20">
              {!isZoomed ? (
                <>
                  <ZoomIn className="h-4 w-4" />
                  <span className="text-xs">Click to zoom</span>
                </>
              ) : !showMagnifier ? (
                <>
                  <Search className="h-4 w-4" />
                  <span className="text-xs">Click for magnifier</span>
                </>
              ) : (
                <>
                  <ZoomIn className="h-4 w-4 rotate-180" />
                  <span className="text-xs">Click to reset</span>
                </>
              )}
            </div>
          )}
        </div>

        {/* Navigation buttons - higher z-index to stay above zoomed image */}
        <button
          onClick={prevImage}
          className="absolute left-0 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-1 rounded-r-md z-40 transition-colors duration-200"
          disabled={isTransitioning}
          aria-label="Previous image"
        >
          <ChevronLeft className="h-6 w-6" />
        </button>
        <button
          onClick={nextImage}
          className="absolute right-0 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-1 rounded-l-md z-40 transition-colors duration-200"
          disabled={isTransitioning}
          aria-label="Next image"
        >
          <ChevronRight className="h-6 w-6" />
        </button>

        {/* Image counter - higher z-index to stay above zoomed image */}
        <div className="absolute bottom-2 left-1/2 -translate-x-1/2 bg-black/50 text-white px-2 py-1 rounded-md text-sm z-40">
          {currentIndex + 1} / {images.length}
        </div>

        {/* Hidden preload images */}
        <div className="hidden">
          {images.map((src, index) => (
            index !== currentIndex && (
              <img
                key={`preload-${index}`}
                src={src}
                alt=""
                onLoad={() => handleImageLoad(index)}
              />
            )
          ))}
        </div>
      </div>
    </div>
  );
};

export default ImageCarousel;
