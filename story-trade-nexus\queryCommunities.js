#!/usr/bin/env node

/**
 * Firebase Firestore Community Query Script
 *
 * This script queries the Firebase Firestore database to retrieve and display
 * community names based on a user-provided pincode input.
 *
 * Usage: node queryCommunities.js <pincode>
 * Example: node queryCommunities.js 500001
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { initializeApp } from 'firebase/app';
import {
  getFirestore,
  collection,
  query,
  where,
  limit as limitQuery,
  getDocs
} from 'firebase/firestore';
import process from 'process';

// Firebase configuration (same as in the application)
const firebaseConfig = {
  apiKey: "AIzaSyAgXbuXyBEa9aEPVB4JtLugCbRWK39Vj_c",
  authDomain: "book-share-98f6a.firebaseapp.com",
  projectId: "book-share-98f6a",
  storageBucket: "book-share-98f6a.firebasestorage.app",
  messagingSenderId: "216941059965",
  appId: "1:216941059965:web:2e0528a8a018ff959c7614",
  measurementId: "G-NYSPR3K1PY"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Configuration constants
const QUERY_LIMIT = 100; // Limit to prevent excessive data retrieval
const COLLECTION_NAME = 'hyderabadProperties';

/**
 * Logger utility for consistent output formatting
 */
const logger = {
  info: (message) => console.log(`ℹ️  ${message}`),
  success: (message) => console.log(`✅ ${message}`),
  error: (message) => console.error(`❌ ${message}`),
  warning: (message) => console.warn(`⚠️  ${message}`),
  data: (message) => console.log(`📊 ${message}`),
};

/**
 * Validates the pincode format
 * @param {string} pincode - The pincode to validate
 * @returns {boolean} - True if valid, false otherwise
 */
function validatePincode(pincode) {
  // Check if pincode is exactly 6 digits
  const pincodeRegex = /^\d{6}$/;
  return pincodeRegex.test(pincode);
}

/**
 * Displays usage instructions
 */
function showUsage() {
  console.log('📋 Usage Instructions:');
  console.log('  node queryCommunities.js <pincode>');
  console.log('  node queryCommunities.js --help');
  console.log('  node queryCommunities.js --version');
  console.log('\n📝 Examples:');
  console.log('  node queryCommunities.js 500001');
  console.log('  node queryCommunities.js 560001');
  console.log('  node queryCommunities.js 400001');
  console.log('\n📌 Requirements:');
  console.log('  - Pincode must be exactly 6 digits');
  console.log('  - No spaces or special characters allowed');
  console.log('  - Must be a valid Indian pincode');
  console.log('\n🔧 Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --version, -v  Show version information');
}

/**
 * Queries Firestore for communities based on pincode
 * @param {string} pincode - The pincode to search for
 * @returns {Promise<string[]>} - Array of unique community names
 */
async function queryCommunities(pincode) {
  try {
    logger.info(`Connecting to Firebase Firestore...`);

    // Convert pincode to number for query (try both string and number formats)
    const pincodeNumber = parseInt(pincode, 10);

    logger.info(`Querying '${COLLECTION_NAME}' collection for pincode: ${pincode}`);

    // Create queries for both string and number formats
    const queries = [
      // Query for pincode as number
      query(
        collection(db, COLLECTION_NAME),
        where('pincode', '==', pincodeNumber),
        limitQuery(QUERY_LIMIT)
      ),
      // Query for pincode as string
      query(
        collection(db, COLLECTION_NAME),
        where('pincode', '==', pincode),
        limitQuery(QUERY_LIMIT)
      )
    ];

    const communities = new Set(); // Use Set to automatically handle duplicates
    let totalDocuments = 0;

    // Execute both queries to handle different data formats
    for (let i = 0; i < queries.length; i++) {
      const queryType = i === 0 ? 'number' : 'string';
      logger.info(`Executing query ${i + 1}/2 (pincode as ${queryType})...`);

      try {
        const querySnapshot = await getDocs(queries[i]);
        const docsFound = querySnapshot.size;
        totalDocuments += docsFound;

        if (docsFound > 0) {
          logger.success(`Found ${docsFound} documents with pincode as ${queryType}`);

          querySnapshot.forEach((doc) => {
            const data = doc.data();
            if (data.communityName && typeof data.communityName === 'string') {
              communities.add(data.communityName.trim());
            }
          });
        } else {
          logger.info(`No documents found with pincode as ${queryType}`);
        }
      } catch (queryError) {
        logger.warning(`Query ${i + 1} failed: ${queryError.message}`);
      }
    }

    logger.data(`Total documents processed: ${totalDocuments}`);
    return Array.from(communities).sort(); // Convert Set to sorted Array

  } catch (error) {
    logger.error(`Database query failed: ${error.message}`);
    throw error;
  }
}

/**
 * Displays the query results in a formatted manner
 * @param {string} pincode - The searched pincode
 * @param {string[]} communities - Array of community names
 */
function displayResults(pincode, communities) {
  console.log('\n' + '='.repeat(60));
  console.log(`🔍 SEARCH RESULTS FOR PINCODE: ${pincode}`);
  console.log('='.repeat(60));

  if (communities.length === 0) {
    logger.warning('No communities found for this pincode');
    console.log('\n💡 Suggestions:');
    console.log('  • Verify the pincode is correct');
    console.log('  • Try a nearby pincode');
    console.log('  • Check if the area is covered in our database');
  } else {
    logger.success(`Found ${communities.length} unique communit${communities.length === 1 ? 'y' : 'ies'}`);

    console.log('\n📍 COMMUNITIES:');
    communities.forEach((community, index) => {
      console.log(`  ${(index + 1).toString().padStart(2, ' ')}. ${community}`);
    });
  }

  console.log('\n' + '='.repeat(60));
}

/**
 * Main function to execute the script
 */
async function main() {
  try {
    // Get command line arguments
    const args = process.argv.slice(2);

    // Check for help flags
    if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
      console.log('\n🏘️  Firebase Community Query Tool');
      console.log('📍 Searching Hyderabad Properties Database\n');
      showUsage();
      process.exit(0);
    }

    // Check for version flags
    if (args.includes('--version') || args.includes('-v')) {
      console.log('\n🏘️  Firebase Community Query Tool');
      console.log('📍 Version 1.0.0');
      console.log('📧 PeerBooks Team');
      console.log('🔗 Firebase Project: book-share-98f6a');
      process.exit(0);
    }

    // Display script header
    console.log('\n🏘️  Firebase Community Query Tool');
    console.log('📍 Searching Hyderabad Properties Database\n');

    if (args.length > 1) {
      logger.warning('Multiple arguments provided. Using the first one as pincode.');
    }

    const pincode = args[0].trim();

    // Validate pincode format
    if (!validatePincode(pincode)) {
      logger.error('Invalid pincode format');
      console.log('\n📋 Pincode Requirements:');
      console.log('  • Must be exactly 6 digits');
      console.log('  • No spaces, letters, or special characters');
      console.log(`  • Your input: "${pincode}"`);
      showUsage();
      process.exit(1);
    }

    logger.info(`Starting search for pincode: ${pincode}`);

    // Query the database
    const communities = await queryCommunities(pincode);

    // Display results
    displayResults(pincode, communities);

    // Exit successfully
    process.exit(0);

  } catch (error) {
    logger.error(`Script execution failed: ${error.message}`);

    // Provide helpful error messages based on error type
    if (error.message.includes('permission-denied')) {
      console.log('\n💡 This might be a permissions issue. Please check:');
      console.log('  • Firebase project configuration');
      console.log('  • Firestore security rules');
      console.log('  • Network connectivity');
    } else if (error.message.includes('not-found')) {
      console.log('\n💡 The collection might not exist. Please verify:');
      console.log('  • Collection name is correct');
      console.log('  • Data has been imported to Firestore');
    } else {
      console.log('\n💡 For troubleshooting:');
      console.log('  • Check your internet connection');
      console.log('  • Verify Firebase project is accessible');
      console.log('  • Ensure the hyderabadProperties collection exists');
    }

    process.exit(1);
  }
}

// Execute the main function
main();
