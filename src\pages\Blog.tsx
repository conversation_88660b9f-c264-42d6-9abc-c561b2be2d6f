import React, { useEffect, useState } from 'react';



import { collection, query, orderBy, getDocs, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../firebase/firebaseConfig'; // Ensure this path is correct
import marked from 'marked';

const Blog: React.FC = () => {
  const [posts, setPosts] = useState([]);

  useEffect(() => {
    const fetchPosts = async () => {
      const q = query(collection(db, 'blogPosts'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      const postsData = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      setPosts(postsData);
    };

    fetchPosts();
  }, []);

  return (
    <div className="max-w-4xl mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Blog</h1>
      {posts.map(post => (
        <div key={post.id} className="mb-8">
          <h2 className="text-2xl font-semibold">{post.title}</h2>
          <p className="text-sm text-gray-500">
            By {post.author} on {new Date(post.createdAt.seconds * 1000).toLocaleDateString()}
          </p>
          <div
            className="prose mt-4"
            dangerouslySetInnerHTML={{ __html: marked(post.content) }}
          />
        </div>
      ))}
    </div>
  );
};

export default Blog;