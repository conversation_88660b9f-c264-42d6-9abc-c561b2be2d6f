#!/usr/bin/env node

/**
 * Debug script to test Firebase connection and identify issues
 */

import { initializeFirebaseAdmin, testFirebaseConnection } from './firebase-config.js';

async function debugFirebase() {
  console.log('🔍 Starting Firebase debug test...');
  console.log('📍 Current working directory:', process.cwd());
  console.log('📍 Script location:', import.meta.url);
  
  // Check environment variables
  console.log('\n🔧 Environment Variables:');
  console.log('GOOGLE_APPLICATION_CREDENTIALS:', process.env.GOOGLE_APPLICATION_CREDENTIALS || 'NOT SET');
  console.log('FIREBASE_PRIVATE_KEY:', process.env.FIREBASE_PRIVATE_KEY ? 'SET (length: ' + process.env.FIREBASE_PRIVATE_KEY.length + ')' : 'NOT SET');
  console.log('FIREBASE_CLIENT_EMAIL:', process.env.FIREBASE_CLIENT_EMAIL || 'NOT SET');
  console.log('FIREBASE_PRIVATE_KEY_ID:', process.env.FIREBASE_PRIVATE_KEY_ID || 'NOT SET');
  console.log('FIREBASE_CLIENT_ID:', process.env.FIREBASE_CLIENT_ID || 'NOT SET');

  try {
    console.log('\n🚀 Attempting to initialize Firebase Admin SDK...');
    const services = await initializeFirebaseAdmin();
    console.log('✅ Firebase Admin SDK initialized successfully');
    
    console.log('\n🧪 Testing Firebase connection...');
    const connectionOk = await testFirebaseConnection();
    
    if (connectionOk) {
      console.log('✅ Firebase connection test passed');
      
      // Test listing files in storage
      console.log('\n📁 Testing storage file listing...');
      const bucket = services.storage.bucket();
      const [files] = await bucket.getFiles({
        prefix: 'book-images/',
        maxResults: 5
      });
      
      console.log(`📊 Found ${files.length} files in book-images/ (showing first 5)`);
      files.forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.name} (${file.metadata.size} bytes)`);
      });
      
    } else {
      console.log('❌ Firebase connection test failed');
    }
    
  } catch (error) {
    console.error('❌ Firebase initialization failed:');
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
  }
}

debugFirebase().catch(console.error);
