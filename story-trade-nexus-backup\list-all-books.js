// Script to list all books from Firebase Firestore
// This script connects to Firestore and displays all books in a formatted table

// Import Firebase modules
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, query, orderBy, doc, getDoc } from 'firebase/firestore';

// Firebase configuration from the project
const firebaseConfig = {
  apiKey: "AIzaSyAgXbuXyBEa9aEPVB4JtLugCbRWK39Vj_c",
  authDomain: "book-share-98f6a.firebaseapp.com",
  projectId: "book-share-98f6a",
  storageBucket: "book-share-98f6a.firebasestorage.app",
  messagingSenderId: "216941059965",
  appId: "1:216941059965:web:2e0528a8a018ff959c7614",
  measurementId: "G-NYSPR3K1PY"
};

// Display a header message
console.log('='.repeat(100));
console.log('BOOK SHARING APP - ALL BOOKS LISTING');
console.log('='.repeat(100));
console.log('Connecting to Firebase Firestore database...');

/**
 * Formats a date object to a readable string
 * @param {Date|Object} date - The date to format
 * @returns {string} Formatted date string
 */
function formatDate(date) {
  if (!date) return 'Unknown';

  try {
    // Check if date is a Firestore timestamp
    if (date.toDate && typeof date.toDate === 'function') {
      date = date.toDate();
    }

    // Check if date is a valid Date object
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      return 'Invalid Date';
    }

    // Format the date
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };

    return date.toLocaleDateString('en-US', options);
  } catch (error) {
    return 'Invalid Date';
  }
}

/**
 * Truncates a string to a specified length and adds ellipsis if needed
 * @param {string} str - The string to truncate
 * @param {number} maxLength - Maximum length of the string
 * @returns {string} Truncated string
 */
function truncate(str, maxLength) {
  if (!str) return '';
  str = String(str);
  return str.length > maxLength ? str.substring(0, maxLength - 3) + '...' : str.padEnd(maxLength);
}

/**
 * Fetches owner email from the users collection if available
 * @param {object} db - Firestore database instance
 * @param {string} ownerId - The ID of the owner
 * @returns {Promise<string|null>} The owner's email or null if not found
 */
async function fetchOwnerEmail(db, ownerId) {
  if (!ownerId) return null;

  try {
    // Check if ownerId is already an email
    if (ownerId.includes('@')) {
      return ownerId;
    }

    // Try to get the user document
    const userRef = doc(db, 'users', ownerId);
    const userSnapshot = await getDoc(userRef);

    if (userSnapshot.exists()) {
      const userData = userSnapshot.data();
      // Check various fields that might contain email
      return userData.email || userData.emailAddress || userData.userEmail || null;
    }

    return null;
  } catch (error) {
    console.error(`Error fetching owner email for ${ownerId}:`, error);
    return null;
  }
}

/**
 * Fetches owner pincode from the users collection if available
 * @param {object} db - Firestore database instance
 * @param {string} ownerId - The ID of the owner
 * @returns {Promise<string|null>} The owner's pincode or null if not found
 */
async function fetchOwnerPincode(db, ownerId) {
  if (!ownerId) return null;

  try {
    // Try to get the user document
    const userRef = doc(db, 'users', ownerId);
    const userSnapshot = await getDoc(userRef);

    if (userSnapshot.exists()) {
      const userData = userSnapshot.data();

      // Check various fields that might contain pincode
      const pincode = userData.pincode ||
                      userData.pinCode ||
                      userData.pin_code ||
                      userData.postalCode ||
                      userData.zipcode ||
                      userData.zip;

      if (pincode) return pincode;

      // Try to extract pincode from address if available
      if (userData.address && typeof userData.address === 'string') {
        const pincodeMatch = userData.address.match(/\b\d{6}\b/); // Indian pincodes are 6 digits
        if (pincodeMatch) {
          return pincodeMatch[0];
        }
      }
    }

    return null;
  } catch (error) {
    console.error(`Error fetching owner pincode for ${ownerId}:`, error);
    return null;
  }
}

/**
 * Retrieves all books from Firestore and displays them in a table
 */
async function listAllBooks() {
  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    console.log('Connected to Firebase successfully.');
    console.log('Retrieving books from the database...');

    // Create a query to get all books sorted by creation date (newest first)
    const booksRef = collection(db, 'books');
    const booksQuery = query(booksRef, orderBy('createdAt', 'desc'));

    // Execute the query
    const querySnapshot = await getDocs(booksQuery);

    if (querySnapshot.empty) {
      console.log('No books found in the database.');
      return;
    }

    // Prepare data for display
    const books = [];

    // Process each book document
    const totalBooks = querySnapshot.size;
    console.log(`Processing ${totalBooks} books and fetching owner information...`);

    let processedCount = 0;
    for (const bookDoc of querySnapshot.docs) {
      const data = bookDoc.data();
      const ownerId = data.ownerId || '';

      // Try to get owner email - first from book data, then from users collection
      let ownerEmail = data.ownerEmail || '';

      if (!ownerEmail && ownerId) {
        const email = await fetchOwnerEmail(db, ownerId);
        if (email) {
          ownerEmail = email;
        } else {
          ownerEmail = ownerId; // Fall back to ownerId if email not found
        }
      }

      // Try to get owner pincode - first from book data, then from users collection
      let ownerPincode = data.ownerPincode || '';

      if (!ownerPincode && ownerId) {
        const pincode = await fetchOwnerPincode(db, ownerId);
        if (pincode) {
          ownerPincode = pincode;
        }
      }

      books.push({
        id: bookDoc.id,
        title: data.title || 'Untitled',
        author: data.author || 'Unknown',
        ownerName: data.ownerName || 'Unknown',
        ownerEmail: ownerEmail || 'Unknown',
        ownerPincode: ownerPincode || 'N/A',
        createdAt: data.createdAt || null,
        approvalStatus: data.approvalStatus || 'Unknown',
        availability: data.availability || 'Unknown',
        hasLocation: !!(data.ownerCoordinates || ownerPincode || data.ownerLocation)
      });

      // Update progress
      processedCount++;
      if (processedCount % 10 === 0 || processedCount === totalBooks) {
        const percent = Math.round((processedCount / totalBooks) * 100);
        process.stdout.write(`\rProgress: ${processedCount}/${totalBooks} books processed (${percent}%)`);
      }
    }

    // Move to next line after progress indicator
    console.log('');

    console.log(`Found ${books.length} books in the database.`);

    // Define column widths for the table
    const columns = {
      id: 10,
      title: 30,
      author: 20,
      ownerName: 20,
      ownerEmail: 25,
      ownerPincode: 12,
      createdAt: 20,
      status: 10,
      location: 8
    };

    // Print table header
    console.log('\n' + '='.repeat(165));
    console.log(
      'ID'.padEnd(columns.id) +
      'TITLE'.padEnd(columns.title) +
      'AUTHOR'.padEnd(columns.author) +
      'OWNER NAME'.padEnd(columns.ownerName) +
      'OWNER EMAIL'.padEnd(columns.ownerEmail) +
      'PINCODE'.padEnd(columns.ownerPincode) +
      'CREATED AT'.padEnd(columns.createdAt) +
      'STATUS'.padEnd(columns.status) +
      'LOCATION'
    );
    console.log('='.repeat(165));

    // Print table rows
    books.forEach(book => {
      console.log(
        truncate(book.id, columns.id) +
        truncate(book.title, columns.title) +
        truncate(book.author, columns.author) +
        truncate(book.ownerName, columns.ownerName) +
        truncate(book.ownerEmail, columns.ownerEmail) +
        truncate(book.ownerPincode, columns.ownerPincode) +
        truncate(formatDate(book.createdAt), columns.createdAt) +
        truncate(book.approvalStatus, columns.status) +
        (book.hasLocation ? 'Yes' : 'No')
      );
    });

    console.log('='.repeat(165));
    console.log(`Total books: ${books.length}`);

    // Count books with location information
    const booksWithLocation = books.filter(book => book.hasLocation).length;
    console.log(`Books with location information: ${booksWithLocation} (${Math.round(booksWithLocation / books.length * 100)}%)`);

    // Count books by approval status
    const approvalStatusCounts = {};
    books.forEach(book => {
      const status = book.approvalStatus || 'Unknown';
      approvalStatusCounts[status] = (approvalStatusCounts[status] || 0) + 1;
    });

    console.log('\nBooks by approval status:');
    Object.entries(approvalStatusCounts).forEach(([status, count]) => {
      console.log(`  ${status}: ${count} (${Math.round(count / books.length * 100)}%)`);
    });

    // Count books by owner email domain
    const emailDomainCounts = {};
    books.forEach(book => {
      if (book.ownerEmail && book.ownerEmail.includes('@')) {
        const domain = book.ownerEmail.split('@')[1];
        emailDomainCounts[domain] = (emailDomainCounts[domain] || 0) + 1;
      }
    });

    if (Object.keys(emailDomainCounts).length > 0) {
      console.log('\nBooks by owner email domain:');
      Object.entries(emailDomainCounts)
        .sort((a, b) => b[1] - a[1]) // Sort by count (descending)
        .forEach(([domain, count]) => {
          console.log(`  ${domain}: ${count} (${Math.round(count / books.length * 100)}%)`);
        });
    }

    // Count unique owners
    const uniqueOwners = new Set();
    books.forEach(book => {
      if (book.ownerEmail) {
        uniqueOwners.add(book.ownerEmail);
      }
    });

    console.log(`\nUnique owners: ${uniqueOwners.size}`);
    console.log(`Average books per owner: ${(books.length / uniqueOwners.size).toFixed(2)}`);

    // List top owners by book count
    const ownerBookCounts = {};
    books.forEach(book => {
      if (book.ownerEmail) {
        ownerBookCounts[book.ownerEmail] = (ownerBookCounts[book.ownerEmail] || 0) + 1;
      }
    });

    if (Object.keys(ownerBookCounts).length > 0) {
      console.log('\nTop 5 owners by book count:');
      Object.entries(ownerBookCounts)
        .sort((a, b) => b[1] - a[1]) // Sort by count (descending)
        .slice(0, 5) // Take top 5
        .forEach(([email, count], index) => {
          console.log(`  ${index + 1}. ${email}: ${count} books`);
        });
    }

    // Count books by pincode
    const pincodeCounts = {};
    let booksWithPincode = 0;

    books.forEach(book => {
      if (book.ownerPincode && book.ownerPincode !== 'N/A') {
        booksWithPincode++;
        pincodeCounts[book.ownerPincode] = (pincodeCounts[book.ownerPincode] || 0) + 1;
      }
    });

    if (Object.keys(pincodeCounts).length > 0) {
      console.log(`\nBooks with pincode information: ${booksWithPincode} (${Math.round(booksWithPincode / books.length * 100)}%)`);
      console.log('\nTop 5 pincodes by book count:');
      Object.entries(pincodeCounts)
        .sort((a, b) => b[1] - a[1]) // Sort by count (descending)
        .slice(0, 5) // Take top 5
        .forEach(([pincode, count], index) => {
          console.log(`  ${index + 1}. ${pincode}: ${count} books (${Math.round(count / books.length * 100)}%)`);
        });
    }

  } catch (error) {
    console.error('Error retrieving books from Firestore:');
    console.error(error);

    // Provide more specific error messages based on common Firebase errors
    if (error.code === 'permission-denied') {
      console.error('\nPermission denied. Make sure you have the correct access rights to the Firestore database.');
    } else if (error.code === 'unavailable') {
      console.error('\nDatabase is currently unavailable. Please check your internet connection and try again.');
    } else if (error.code === 'not-found') {
      console.error('\nDatabase or collection not found. Make sure the collection path is correct.');
    } else if (error.code === 'invalid-argument') {
      console.error('\nInvalid query. Check the query parameters and try again.');
    }

    process.exit(1);
  }
}

// Self-executing async function
(async () => {
  try {
    await listAllBooks();
  } catch (error) {
    console.error('Unhandled error:', error);
    process.exit(1);
  }
})();
