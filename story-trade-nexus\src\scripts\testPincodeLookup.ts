/**
 * Test script for pincode lookup functionality
 */

import { lookupPincodeLocation, batchProcessPincodes } from '../lib/pincodeService';

// Sample pincodes to test (common Indian pincodes from different regions)
const testPincodes = [
  '110001', // New Delhi
  '400001', // Mumbai
  '600001', // Chennai
  '700001', // Kolkata
  '500001', // Hyderabad
  '380001', // Ahmedabad
  '560001', // Bangalore
  '226001', // Lucknow
  '800001', // Patna
  '302001'  // Jaipur
];

// Self-executing async function
(async () => {
  console.log('Starting pincode lookup test...');
  
  try {
    // Test individual pincode lookup
    console.log('\n=== Testing individual pincode lookup ===');
    for (const pincode of testPincodes.slice(0, 3)) { // Test first 3 pincodes
      console.log(`\nLooking up pincode: ${pincode}`);
      const locationData = await lookupPincodeLocation(pincode);
      
      if (locationData) {
        console.log('✅ Success!');
        console.log('Location data:', JSON.stringify(locationData, null, 2));
      } else {
        console.error(`❌ Failed to lookup pincode: ${pincode}`);
      }
    }
    
    // Test batch processing
    console.log('\n=== Testing batch processing ===');
    console.log('Processing batch of pincodes:', testPincodes);
    const batchResults = await batchProcessPincodes(testPincodes, 1000);
    
    console.log(`\n✅ Batch processing complete. Processed ${batchResults.size} pincodes.`);
    console.log('Results:');
    
    batchResults.forEach((locationData, pincode) => {
      console.log(`\nPincode: ${pincode}`);
      console.log('Location data:', JSON.stringify(locationData, null, 2));
    });
    
    // Test invalid pincode
    console.log('\n=== Testing invalid pincode ===');
    const invalidPincode = '123'; // Invalid format (too short)
    console.log(`Looking up invalid pincode: ${invalidPincode}`);
    const invalidResult = await lookupPincodeLocation(invalidPincode);
    
    if (invalidResult) {
      console.log('Unexpected success with invalid pincode!');
      console.log('Location data:', JSON.stringify(invalidResult, null, 2));
    } else {
      console.log('✅ Correctly handled invalid pincode');
    }
    
    // Test non-existent pincode
    console.log('\n=== Testing non-existent pincode ===');
    const nonExistentPincode = '999999'; // Non-existent pincode
    console.log(`Looking up non-existent pincode: ${nonExistentPincode}`);
    const nonExistentResult = await lookupPincodeLocation(nonExistentPincode);
    
    if (nonExistentResult) {
      console.log('Unexpected success with non-existent pincode!');
      console.log('Location data:', JSON.stringify(nonExistentResult, null, 2));
    } else {
      console.log('✅ Correctly handled non-existent pincode');
    }
    
    console.log('\n=== Test complete ===');
  } catch (error) {
    console.error('❌ Error running test script:', error);
  }
  
  // Force exit after completion
  process.exit(0);
})();
