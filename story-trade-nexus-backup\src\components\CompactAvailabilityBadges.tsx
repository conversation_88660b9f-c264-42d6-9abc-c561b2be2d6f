import React from 'react';
import { Badge } from '@/components/ui/badge';

interface CompactAvailabilityBadgesProps {
  availability: string;
  className?: string;
}

const CompactAvailabilityBadges: React.FC<CompactAvailabilityBadgesProps> = ({
  availability,
  className = ''
}) => {
  // Check if the book is available for exchange
  const isExchangeAvailable =
    availability === 'For Exchange' ||
    availability === 'For Rent & Exchange' ||
    availability === 'For Sale & Exchange' ||
    availability === 'For Rent, Sale & Exchange';

  // Check if the book is available for rent
  const isRentAvailable =
    availability === 'For Rent' ||
    availability === 'For Rent & Sale' ||
    availability === 'For Rent & Exchange' ||
    availability === 'For Rent, Sale & Exchange';

  // Check if the book is available for sale
  const isSaleAvailable =
    availability === 'For Sale' ||
    availability === 'For Rent & Sale' ||
    availability === 'For Sale & Exchange' ||
    availability === 'For Rent, Sale & Exchange';

  return (
    <div className={`flex flex-wrap gap-2 mb-5 ${className}`}>
      {isRentAvailable && (
        <Badge className="bg-blue-500 text-white hover:bg-blue-600 px-3 py-1 rounded-full">
          For Rent
        </Badge>
      )}

      {isSaleAvailable && (
        <Badge className="bg-green-500 text-white hover:bg-green-600 px-3 py-1 rounded-full">
          For Sale
        </Badge>
      )}

      {isExchangeAvailable && (
        <Badge className="bg-purple-500 text-white hover:bg-purple-600 px-3 py-1 rounded-full">
          For Exchange
        </Badge>
      )}
    </div>
  );
};

export default CompactAvailabilityBadges;
