import React, { useState } from 'react';
import MainLayout from '@/components/layouts/MainLayout';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface FAQItemProps {
  question: string;
  answer: React.ReactNode;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200 py-4">
      <button
        className="flex justify-between items-center w-full text-left font-medium text-navy-800 hover:text-burgundy-500 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-lg">{question}</span>
        {isOpen ? (
          <ChevronUp className="h-5 w-5 flex-shrink-0 text-burgundy-500" />
        ) : (
          <ChevronDown className="h-5 w-5 flex-shrink-0 text-gray-500" />
        )}
      </button>
      {isOpen && (
        <div className="mt-2 text-gray-600 leading-relaxed">
          {answer}
        </div>
      )}
    </div>
  );
};

const FAQ: React.FC = () => {
  const accountFAQs = [
    {
      question: "How do I create an account on PeerBooks?",
      answer: (
        <p>
          Creating an account on PeerBooks is simple. Click on the "Join Now" button on the homepage. You'll need to provide your email address, create a password, and fill in basic information including your name, phone number, and address details. We also require your location information to help connect you with books nearby. Once you've completed the registration form, you'll receive a verification email. Click the link in the email to verify your account and start using PeerBooks.
        </p>
      ),
    },
    {
      question: "Is my personal information secure on PeerBooks?",
      answer: (
        <p>
          Yes, we take data security very seriously. Your personal information is encrypted and stored securely. We only share your approximate location with other users to calculate distance, and your exact address is never revealed. Your phone number is only shared when you explicitly connect with another user for a book transaction. You can review our privacy policy for more details on how we handle your data.
        </p>
      ),
    },
    {
      question: "Can I use PeerBooks without sharing my location?",
      answer: (
        <p>
          While you can browse books without sharing your location, providing your location is essential for the core functionality of PeerBooks. Location information helps us show you books available near you and calculate distances between you and book owners. Without location access, you'll have limited functionality on the platform. You can always adjust your location permissions in your account settings.
        </p>
      ),
    },
  ];

  const listingFAQs = [
    {
      question: "How do I list a book on PeerBooks?",
      answer: (
        <p>
          To list a book, log in to your account and click on "Add Book" in the navigation menu. Fill in the required details including title, author, genre, condition, and description. You can upload up to 4 images of your book, with one designated as the display image. Specify your preferred transaction type (sale, rent, or exchange) and set your price or terms. Once submitted, your listing will be reviewed by our admin team before becoming visible to other users.
        </p>
      ),
    },
    {
      question: "What information should I include in my book listing?",
      answer: (
        <p>
          A good book listing should include: an accurate title and author name, the correct genre(s), an honest assessment of the book's condition, a detailed description mentioning any highlights or damage, clear images showing the book from multiple angles, and fair pricing if you're selling or renting. The more information you provide, the more likely you are to find interested users.
        </p>
      ),
    },
    {
      question: "How many images can I upload for each book?",
      answer: (
        <p>
          You can upload up to 4 images for each book. We recommend including photos of the front cover, back cover, spine, and a sample of the inside pages to show the condition. You can designate one image as the display image, which will be shown in search results and book tiles.
        </p>
      ),
    },
    {
      question: "How long does it take for my book listing to be approved?",
      answer: (
        <p>
          Book listings are typically reviewed and approved within 24-48 hours. Our admin team checks each listing to ensure it meets our community guidelines. You'll receive a notification once your book is approved and visible to other users. If your listing is rejected, you'll receive feedback explaining why, and you can make the necessary changes and resubmit.
        </p>
      ),
    },
  ];

  const transactionFAQs = [
    {
      question: "How do the different transaction types work on PeerBooks?",
      answer: (
        <div>
          <p>PeerBooks supports three transaction types:</p>
          <ul className="list-disc pl-5 mt-2 space-y-1">
            <li><strong>Sale:</strong> You set a fixed price and sell your book permanently to another user.</li>
            <li><strong>Rent:</strong> You lend your book for a specified period (day, week, or month) at a set rental price. You may require a security deposit.</li>
            <li><strong>Exchange:</strong> You swap books with another user, with the option to request compensation for value differences (typically 20-30% of the book's value).</li>
          </ul>
          <p className="mt-2">PeerBooks only facilitates connections between users and does not handle payments or disputes directly.</p>
        </div>
      ),
    },
    {
      question: "What is the security deposit for rentals and how does it work?",
      answer: (
        <p>
          A security deposit is an optional amount that book owners can require when renting out their books. It serves as protection against damage, loss, or late returns. When setting up a rental listing, you can specify if a security deposit is required and set the amount. The security deposit is typically returned to the renter when the book is returned in good condition. The transaction is handled directly between users, and PeerBooks does not manage the security deposit payments.
        </p>
      ),
    },
    {
      question: "How are payments handled on PeerBooks?",
      answer: (
        <p>
          PeerBooks does not process payments directly. We connect book owners and interested users, but the actual payment transaction happens between users. You can discuss and agree on payment methods with the other party. Common options include cash on delivery, digital payment apps, or bank transfers. Always ensure you're comfortable with the payment arrangement before proceeding with a transaction.
        </p>
      ),
    },
    {
      question: "What happens if a book is damaged during rental?",
      answer: (
        <p>
          If a book is damaged during a rental period, the security deposit (if required) can be used to cover repairs or replacement. We recommend that both parties document the condition of the book with photos before and after the rental period. If a dispute arises, users should try to resolve it amicably. PeerBooks does not mediate disputes but may consider account restrictions for users who repeatedly violate community guidelines.
        </p>
      ),
    },
  ];

  const findingBooksFAQs = [
    {
      question: "How do I find books near me?",
      answer: (
        <p>
          The home page and browse section display books sorted by distance from your location. You can use the search bar to find specific titles, authors, or genres. Each book listing shows the approximate distance between you and the book owner. You can also filter results by availability type (for sale, for rent, for exchange) and other criteria to narrow down your search.
        </p>
      ),
    },
    {
      question: "How is the distance between users calculated?",
      answer: (
        <p>
          Distance is calculated based on the GPS coordinates of both users. When you allow location access, we use your current coordinates to calculate the straight-line distance to each book owner. This helps you find books that are conveniently located near you. The distance is displayed in kilometers on each book listing.
        </p>
      ),
    },
    {
      question: "Can I see the exact location of a book owner?",
      answer: (
        <p>
          No, for privacy and security reasons, we don't show the exact location of book owners. You'll only see the approximate distance between you and the owner, and in some cases, their pincode/postal code area. The exact address is only shared when both parties agree to proceed with a transaction.
        </p>
      ),
    },
    {
      question: "How do I contact a book owner?",
      answer: (
        <p>
          When you find a book you're interested in, click on the "Contact Owner" button on the book details page. This will give you options to connect with the owner via WhatsApp using their registered phone number. A pre-filled message about the book will be included. The owner will also receive an email notification about your interest.
        </p>
      ),
    },
  ];

  const ratingsFAQs = [
    {
      question: "How does the rating system work?",
      answer: (
        <p>
          After completing a transaction, both parties can rate each other on a scale of 1-5 stars. Ratings help build trust in the community and provide feedback on user reliability. Your overall rating is displayed on your profile and on your book listings. Maintaining a high rating increases your chances of successful transactions on the platform.
        </p>
      ),
    },
    {
      question: "What should I do if I receive an unfair rating?",
      answer: (
        <p>
          If you believe you've received an unfair rating, you can contact our support team with details of the transaction and why you think the rating is unjustified. While we generally don't remove ratings, we may investigate cases where there's evidence of misuse or malicious intent. The best approach is to maintain professional interactions and clear communication with all users.
        </p>
      ),
    },
  ];

  const technicalFAQs = [
    {
      question: "What should I do if the app can't access my location?",
      answer: (
        <p>
          If the app can't access your location, first check your device settings to ensure you've granted location permissions to PeerBooks. On most devices, you can find this in Settings &gt; Privacy &gt; Location Services. If you're using a browser, make sure you've allowed location access when prompted. If problems persist, try using a different browser or device, or contact our support team for assistance.
        </p>
      ),
    },
    {
      question: "How do I update my location if I move to a new place?",
      answer: (
        <p>
          You can update your location information in your account settings. Navigate to your profile, select "Edit Profile," and update your address details and GPS location. Your new location will be used for all future distance calculations. Remember to keep your location information current to ensure you see relevant book listings in your area.
        </p>
      ),
    },
    {
      question: "What image formats are supported for book photos?",
      answer: (
        <p>
          PeerBooks supports common image formats including JPEG, PNG, and WebP. Images should be clear and well-lit to showcase your book properly. There's a maximum file size of 5MB per image. If your image is too large, consider compressing it before uploading. For best results, take photos in good lighting with a neutral background.
        </p>
      ),
    },
    {
      question: "Is PeerBooks available as a mobile app?",
      answer: (
        <p>
          Currently, PeerBooks is available as a web application optimized for both desktop and mobile browsers. You can access all features by visiting our website on any device. We're working on dedicated mobile apps for iOS and Android, which will be released in the future. For the best mobile experience, we recommend adding PeerBooks to your home screen from your mobile browser.
        </p>
      ),
    },
  ];

  const additionalFAQs = [
    {
      question: "How can I contact PeerBooks support?",
      answer: (
        <p>
          If you have questions or issues not covered in these FAQs, you can reach our support team <NAME_EMAIL>. We aim to respond to all inquiries within 48 hours. For urgent matters, you can use the live chat feature available on our website during business hours. We're here to help make your PeerBooks experience as smooth as possible.
        </p>
      ),
    },
  ];

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-beige-500 to-beige-100 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-playfair font-bold text-navy-800 mb-6">
              Frequently Asked Questions
            </h1>
            <p className="text-lg text-gray-700 mb-8">
              Find answers to common questions about using the PeerBooks platform.
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Account & Registration */}
            <div className="mb-12">
              <h2 className="text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200">
                Account & Registration
              </h2>
              <div className="space-y-1">
                {accountFAQs.map((faq, index) => (
                  <FAQItem key={index} question={faq.question} answer={faq.answer} />
                ))}
              </div>
            </div>

            {/* Listing Books */}
            <div className="mb-12">
              <h2 className="text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200">
                Listing Books
              </h2>
              <div className="space-y-1">
                {listingFAQs.map((faq, index) => (
                  <FAQItem key={index} question={faq.question} answer={faq.answer} />
                ))}
              </div>
            </div>

            {/* Transactions & Payments */}
            <div className="mb-12">
              <h2 className="text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200">
                Transactions & Payments
              </h2>
              <div className="space-y-1">
                {transactionFAQs.map((faq, index) => (
                  <FAQItem key={index} question={faq.question} answer={faq.answer} />
                ))}
              </div>
            </div>

            {/* Finding & Borrowing Books */}
            <div className="mb-12">
              <h2 className="text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200">
                Finding & Borrowing Books
              </h2>
              <div className="space-y-1">
                {findingBooksFAQs.map((faq, index) => (
                  <FAQItem key={index} question={faq.question} answer={faq.answer} />
                ))}
              </div>
            </div>

            {/* Ratings & Reviews */}
            <div className="mb-12">
              <h2 className="text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200">
                Ratings & Reviews
              </h2>
              <div className="space-y-1">
                {ratingsFAQs.map((faq, index) => (
                  <FAQItem key={index} question={faq.question} answer={faq.answer} />
                ))}
              </div>
            </div>

            {/* Technical Questions */}
            <div className="mb-12">
              <h2 className="text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200">
                Technical Questions
              </h2>
              <div className="space-y-1">
                {technicalFAQs.map((faq, index) => (
                  <FAQItem key={index} question={faq.question} answer={faq.answer} />
                ))}
              </div>
            </div>

            {/* Additional Help */}
            <div className="mb-12">
              <h2 className="text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200">
                Additional Help
              </h2>
              <div className="space-y-1">
                {additionalFAQs.map((faq, index) => (
                  <FAQItem key={index} question={faq.question} answer={faq.answer} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    </MainLayout>
  );
};

export default FAQ;
