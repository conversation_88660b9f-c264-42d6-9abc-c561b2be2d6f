
import React, { Suspense } from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/lib/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import AdminRoute from "@/components/auth/AdminRoute";
import ErrorBoundary from "@/components/ErrorBoundary";
import { Skeleton } from "@/components/ui/skeleton";
import { PageLoader, BookDetailLoader, DashboardLoader, AdminLoader } from "@/components/PageLoader";
import LCPMonitor from "@/components/LCPMonitor";

// Immediate load components (critical for first page load)
import Index from "./pages/Index";
import SignIn from "./pages/SignIn";
import Join from "./pages/Join";
import NotFound from "./pages/NotFound";
import Unauthorized from "./pages/Unauthorized";

// Lazy load heavy components
const BookDetail = React.lazy(() => import("./pages/BookDetail"));
const MyBooks = React.lazy(() => import("./pages/MyBooks"));
const AddBooks = React.lazy(() => import("./pages/AddBooks"));
const BrowseBooks = React.lazy(() => import("./pages/BrowseBooks"));
const UserAccount = React.lazy(() => import("./pages/UserAccount"));

// Lazy load admin components
const AdminDashboard = React.lazy(() => import("./pages/AdminDashboard"));
const AdminBookApprovals = React.lazy(() => import("./pages/AdminBookApprovals"));
const AdminUsers = React.lazy(() => import("./pages/AdminUsers"));
const AdminBlogManager = React.lazy(() => import("./pages/AdminBlogManager"));
const AdminUtilities = React.lazy(() => import("./pages/AdminUtilities"));
const AdminSettings = React.lazy(() => import("./pages/AdminSettings"));
const AdminContactMessages = React.lazy(() => import("./pages/AdminContactMessages"));

// Lazy load other pages
const ForgotPassword = React.lazy(() => import("./pages/ForgotPassword"));
const VerifyEmail = React.lazy(() => import("./pages/VerifyEmail"));
const HowItWorksPage = React.lazy(() => import("./pages/HowItWorks"));
const FAQ = React.lazy(() => import("./pages/FAQ"));
const ContactUs = React.lazy(() => import("./pages/ContactUs"));
const Feedback = React.lazy(() => import("./pages/Feedback"));
const PrivacyPolicy = React.lazy(() => import("./pages/PrivacyPolicy"));
const Terms = React.lazy(() => import("./pages/Terms"));
const DataDeletion = React.lazy(() => import("./pages/DataDeletion"));
const SeedBooks = React.lazy(() => import("./pages/SeedBooks"));
const DatabaseBooks = React.lazy(() => import("./pages/DatabaseBooks"));
const AdminSetup = React.lazy(() => import("./pages/AdminSetup"));
const AdminDiagnostic = React.lazy(() => import("./pages/AdminDiagnostic"));
const AdminFeedback = React.lazy(() => import("./pages/AdminFeedback"));

const queryClient = new QueryClient();

const App = () => (
  <BrowserRouter>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          {/* LCP Performance Monitor for development */}
          <LCPMonitor
            enabled={process.env.NODE_ENV === 'development'}
            showDebugInfo={process.env.NODE_ENV === 'development'}
          />
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<Index />} />
            <Route path="/signin" element={<SignIn />} />
            <Route path="/join" element={<Join />} />
            <Route path="/forgot-password" element={
              <Suspense fallback={<PageLoader message="Loading forgot password page..." />}>
                <ForgotPassword />
              </Suspense>
            } />
            <Route path="/verify-email" element={
              <Suspense fallback={<PageLoader message="Loading email verification..." />}>
                <VerifyEmail />
              </Suspense>
            } />
            <Route path="/browse" element={
              <Suspense fallback={<PageLoader message="Loading books..." />}>
                <BrowseBooks />
              </Suspense>
            } />
            <Route path="/books/:id" element={
              <Suspense fallback={<BookDetailLoader />}>
                <BookDetail />
              </Suspense>
            } />
            <Route path="/how-it-works" element={
              <Suspense fallback={<PageLoader message="Loading How It Works..." />}>
                <HowItWorksPage />
              </Suspense>
            } />
            <Route path="/faq" element={
              <Suspense fallback={<PageLoader message="Loading FAQ..." />}>
                <FAQ />
              </Suspense>
            } />
            <Route path="/contact" element={
              <Suspense fallback={<PageLoader message="Loading Contact Us..." />}>
                <ContactUs />
              </Suspense>
            } />
            <Route path="/feedback" element={
              <Suspense fallback={<PageLoader message="Loading Feedback..." />}>
                <Feedback />
              </Suspense>
            } />
            <Route path="/privacy" element={
              <Suspense fallback={<PageLoader message="Loading Privacy Policy..." />}>
                <PrivacyPolicy />
              </Suspense>
            } />
            <Route path="/terms" element={
              <Suspense fallback={<PageLoader message="Loading Terms of Service..." />}>
                <Terms />
              </Suspense>
            } />
            <Route path="/data-deletion" element={
              <Suspense fallback={<PageLoader message="Loading Data Deletion..." />}>
                <DataDeletion />
              </Suspense>
            } />

            {/* Protected routes - require authentication and email verification */}
            <Route path="/dashboard" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your dashboard"
                verificationMessage="You need to verify your email address before you can access your dashboard and manage your books."
              >
                <Suspense fallback={<DashboardLoader />}>
                  <UserAccount />
                </Suspense>
              </ProtectedRoute>
            } />

            <Route path="/profile" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your profile"
              >
                <UserAccount />
              </ProtectedRoute>
            } />

            <Route path="/my-books" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your books"
              >
                <Suspense fallback={<DashboardLoader />}>
                  <MyBooks />
                </Suspense>
              </ProtectedRoute>
            } />

            <Route path="/settings" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your settings"
              >
                <UserAccount />
              </ProtectedRoute>
            } />

            <Route path="/add-books" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="add new books"
                verificationMessage="You need to verify your email address before you can add books to the platform. This helps ensure the quality and security of our book-sharing community."
              >
                <Suspense fallback={<PageLoader message="Loading add books form..." />}>
                  <AddBooks />
                </Suspense>
              </ProtectedRoute>
            } />



            <Route path="/wishlist" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your wishlist"
              >
                <div>Wishlist Page (Coming Soon)</div>
              </ProtectedRoute>
            } />

            <Route path="/messages" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your messages"
              >
                <div>Messages Page (Coming Soon)</div>
              </ProtectedRoute>
            } />

            {/* Development routes */}
            <Route path="/seed-books" element={<SeedBooks />} />
            <Route path="/database-books" element={<DatabaseBooks />} />
            <Route path="/admin-setup" element={<AdminSetup />} />
            <Route path="/admin-diagnostic" element={<AdminDiagnostic />} />

            {/* Admin routes */}
            <Route path="/unauthorized" element={<Unauthorized />} />

            <Route path="/admin" element={
              <ErrorBoundary>
                <AdminRoute>
                  <Suspense fallback={<AdminLoader />}>
                    <AdminDashboard />
                  </Suspense>
                </AdminRoute>
              </ErrorBoundary>
            } />

            <Route path="/admin/books" element={
              <ErrorBoundary>
                <AdminRoute>
                  <Suspense fallback={<PageLoader message="Loading book approvals..." />}>
                    <AdminBookApprovals />
                  </Suspense>
                </AdminRoute>
              </ErrorBoundary>
            } />

            <Route path="/admin/users" element={
              <ErrorBoundary>
                <AdminRoute>
                  <Suspense fallback={<PageLoader message="Loading user management..." />}>
                    <AdminUsers />
                  </Suspense>
                </AdminRoute>
              </ErrorBoundary>
            } />

            <Route path="/admin/blogs" element={
              <ErrorBoundary>
                <AdminRoute>
                  <Suspense fallback={<PageLoader message="Loading blog manager..." />}>
                    <AdminBlogManager />
                  </Suspense>
                </AdminRoute>
              </ErrorBoundary>
            } />

            <Route path="/admin/utilities" element={
              <ErrorBoundary>
                <AdminRoute>
                  <Suspense fallback={<PageLoader message="Loading admin utilities..." />}>
                    <AdminUtilities />
                  </Suspense>
                </AdminRoute>
              </ErrorBoundary>
            } />

            <Route path="/admin/settings" element={
              <ErrorBoundary>
                <AdminRoute>
                  <Suspense fallback={<PageLoader message="Loading admin settings..." />}>
                    <AdminSettings />
                  </Suspense>
                </AdminRoute>
              </ErrorBoundary>
            } />

            <Route path="/admin/messages" element={
              <ErrorBoundary>
                <AdminRoute>
                  <Suspense fallback={<PageLoader message="Loading contact messages..." />}>
                    <AdminContactMessages />
                  </Suspense>
                </AdminRoute>
              </ErrorBoundary>
            } />

            <Route path="/admin/feedback" element={
              <ErrorBoundary>
                <AdminRoute>
                  <Suspense fallback={<PageLoader message="Loading feedback..." />}>
                    <AdminFeedback />
                  </Suspense>
                </AdminRoute>
              </ErrorBoundary>
            } />

            {/* Catch-all route for 404 */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  </BrowserRouter>
);

export default App;

