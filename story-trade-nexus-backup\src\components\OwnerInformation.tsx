/**
 * OwnerInformation Component
 * 
 * Lazy-loaded component for displaying book owner information including:
 * - Owner name and rating
 * - Location information (GPS coordinates, pincode, community)
 * - Distance calculations
 * - Contact functionality
 * - Book status
 */

import React from 'react';
import {
  MapPin,
  Star,
  User,
  MessageCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { BookStatusBadge } from '@/components/BookStatusBadge';
import { Book } from '@/types';
import { GeoCoordinates } from '@/lib/geolocationUtils';

interface OwnerInformationProps {
  book: Book;
  distance: number | null;
  userLocation: GeoCoordinates | null;
  ownerPincode: string | null;
  locationPermission: 'granted' | 'denied' | 'unknown';
  onContactOwner: () => void;
  onRequestLocation: () => void;
}

const OwnerInformation: React.FC<OwnerInformationProps> = ({
  book,
  distance,
  userLocation,
  ownerPincode,
  locationPermission,
  onContactOwner,
  onRequestLocation
}) => {
  return (
    <div className="w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm">
      <h3 className="font-medium text-navy-800 mb-4 text-lg">Owner Information</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex flex-col p-3 bg-gray-50 rounded-md">
          <div className="flex items-center">
            <User className="h-5 w-5 mr-3 text-navy-400" />
            <span className="font-medium">{book.ownerName}</span>
          </div>
        </div>

        {/* Location Information */}
        {/* Case 1: GPS coordinates available - show distance as clickable Google Maps link */}
        {book.ownerCoordinates && (
          <div className="flex items-center p-3 bg-gray-50 rounded-md">
            <MapPin className="h-5 w-5 mr-3 text-burgundy-400" />
            <div className="flex-1">
              <a
                href={`https://www.google.com/maps?q=${book.ownerCoordinates.latitude},${book.ownerCoordinates.longitude}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-burgundy-600 hover:underline font-medium block"
              >
                {distance !== null
                  ? `${distance.toFixed(1)} km away from you`
                  : book.distance
                    ? `${typeof book.distance === 'number'
                        ? book.distance.toFixed(1)
                        : book.distance} km away from you`
                    : `View on map`}
              </a>
              {book.ownerCommunity && (
                <div className="flex items-center mt-1">
                  <span className="text-sm text-blue-600 font-medium">{book.ownerCommunity}</span>
                </div>
              )}
            </div>
            {userLocation && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600"
                onClick={onRequestLocation}
                title="Refresh distance calculation"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
                </svg>
              </Button>
            )}
          </div>
        )}

        {/* Case 2: No GPS coordinates but pincode available - show pincode as location */}
        {!book.ownerCoordinates && (ownerPincode || book.ownerPincode || (book as any).ownerPincode) && (
          <div className="flex items-center p-3 bg-gray-50 rounded-md">
            <MapPin className="h-5 w-5 mr-3 text-burgundy-400" />
            <div className="flex-1">
              <span className="font-medium">
                Location: Pincode {ownerPincode || book.ownerPincode || (book as any).ownerPincode}
              </span>
              {book.ownerCommunity && (
                <div className="flex items-center mt-1">
                  <div className="h-1.5 w-1.5 bg-blue-500 rounded-full mr-2"></div>
                  <span className="text-sm text-blue-600 font-medium">{book.ownerCommunity}</span>
                </div>
              )}
            </div>
            {!locationPermission || locationPermission === 'unknown' ? (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600"
                onClick={onRequestLocation}
                title="Get your location"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <circle cx="12" cy="12" r="1"></circle>
                </svg>
              </Button>
            ) : null}
          </div>
        )}

        {/* Dedicated Pincode display - show when available regardless of GPS coordinates */}
        {(ownerPincode || book.ownerPincode || (book as any).ownerPincode) && book.ownerCoordinates && (
          <div className="flex items-center p-3 bg-gray-50 rounded-md">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-3 text-navy-400">
              <rect x="3" y="8" width="18" height="12" rx="2" />
              <path d="M7 12h10" />
              <path d="M7 16h10" />
              <path d="M11 8V4H8" />
            </svg>
            <span className="font-medium">
              Pincode: {ownerPincode || book.ownerPincode || (book as any).ownerPincode}
            </span>
          </div>
        )}

        <div className="flex items-center p-3 bg-gray-50 rounded-md">
          <Star className="h-5 w-5 mr-3 text-yellow-500" />
          <span className="font-medium">{book.ownerRating}/5 Rating</span>
        </div>

        {/* Book Status in Owner Section */}
        <div className="p-3 bg-gray-50 rounded-md">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Book Status</span>
            <BookStatusBadge
              status={book.status}
              nextAvailableDate={book.nextAvailableDate}
              className="text-xs"
            />
          </div>
          {book.status === 'Rented Out' && book.nextAvailableDate && (
            <div className="mt-2 text-xs text-gray-500">
              Expected return: {book.nextAvailableDate.toLocaleDateString('en-IN', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })}
            </div>
          )}
        </div>

        {/* Community/Location Information - Show when no GPS coordinates or pincode */}
        {!book.ownerCoordinates && !(ownerPincode || book.ownerPincode || (book as any).ownerPincode) && (
          book.ownerCommunity ? (
            <div className="flex items-center p-3 bg-blue-50 rounded-md">
              <div className="h-5 w-5 mr-3 flex items-center justify-center">
                <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
              </div>
              <div>
                <span className="text-sm text-blue-600">Community</span>
                <div className="font-medium text-blue-700">{book.ownerCommunity}</div>
              </div>
            </div>
          ) : book.ownerLocation && book.ownerLocation !== "Unknown Location" ? (
            <div className="flex items-center p-3 bg-gray-50 rounded-md">
              <MapPin className="h-5 w-5 mr-3 text-gray-400" />
              <div>
                <span className="text-sm text-gray-600">Location</span>
                <div className="font-medium text-gray-700">{book.ownerLocation}</div>
              </div>
            </div>
          ) : null
        )}
      </div>

      {/* Case 3: Neither GPS coordinates nor pincode available - show generic message with location request button */}
      {!book.ownerCoordinates && !ownerPincode && !book.ownerPincode && !(book as any).ownerPincode && (
        <div className="flex items-center p-3 bg-gray-50 rounded-md">
          <MapPin className="h-5 w-5 mr-3 text-burgundy-400" />
          <span className="font-medium text-gray-600">
            Location information unavailable
          </span>
          {!locationPermission || locationPermission === 'unknown' ? (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600"
              onClick={onRequestLocation}
              title="Get your location"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <circle cx="12" cy="12" r="1"></circle>
              </svg>
            </Button>
          ) : null}
        </div>
      )}

      <Button
        onClick={onContactOwner}
        className="w-full mt-5"
        size="lg"
        disabled={book.status === 'Sold Out'}
        variant={book.status === 'Sold Out' ? 'outline' : 'default'}
      >
        <MessageCircle className="h-5 w-5 mr-2" />
        {book.status === 'Sold Out' ? 'Book Sold Out' : 'Contact Owner'}
      </Button>
    </div>
  );
};

export default OwnerInformation;
