/**
 * OwnerInformationSkeleton Component
 *
 * Enhanced loading skeleton for the Owner Information section
 * Provides immediate visual feedback for LCP optimization
 * Matches the exact dimensions and layout of the final component
 */

import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

interface OwnerInformationSkeletonProps {
  /** Show enhanced skeleton with more details */
  enhanced?: boolean;
  /** Custom className for styling */
  className?: string;
}

const OwnerInformationSkeleton: React.FC<OwnerInformationSkeletonProps> = ({
  enhanced = false,
  className = ""
}) => {
  return (
    <div className={`w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm ${className}`}>
      {/* Header with shimmer effect */}
      <div className="flex items-center mb-4">
        <Skeleton className="h-6 w-40 animate-pulse" />
      </div>

      {/* Grid of information cards - matches exact layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Owner name card */}
        <div className="flex flex-col p-3 bg-gray-50 rounded-md">
          <div className="flex items-center">
            <Skeleton className="h-5 w-5 mr-3 rounded animate-pulse" />
            <Skeleton className="h-5 w-24 animate-pulse" />
          </div>
        </div>

        {/* Location card */}
        <div className="flex items-center p-3 bg-gray-50 rounded-md">
          <Skeleton className="h-5 w-5 mr-3 rounded animate-pulse" />
          <div className="flex-1">
            <Skeleton className="h-4 w-32 mb-1 animate-pulse" />
            <Skeleton className="h-3 w-20 animate-pulse" />
          </div>
          <Skeleton className="h-6 w-6 ml-2 rounded animate-pulse" />
        </div>

        {/* Rating card */}
        <div className="flex items-center p-3 bg-gray-50 rounded-md">
          <Skeleton className="h-5 w-5 mr-3 rounded animate-pulse" />
          <div className="flex items-center">
            <Skeleton className="h-5 w-20 animate-pulse" />
          </div>
        </div>

        {/* Status card */}
        <div className="p-3 bg-gray-50 rounded-md">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-16 animate-pulse" />
            <Skeleton className="h-6 w-16 rounded-full animate-pulse" />
          </div>
        </div>

        {enhanced && (
          <>
            {/* Additional info cards for enhanced skeleton */}
            <div className="flex items-center p-3 bg-gray-50 rounded-md">
              <Skeleton className="h-5 w-5 mr-3 rounded animate-pulse" />
              <Skeleton className="h-4 w-28 animate-pulse" />
            </div>

            <div className="flex items-center p-3 bg-gray-50 rounded-md">
              <Skeleton className="h-5 w-5 mr-3 rounded animate-pulse" />
              <div className="flex-1">
                <Skeleton className="h-3 w-16 mb-1 animate-pulse" />
                <Skeleton className="h-4 w-24 animate-pulse" />
              </div>
            </div>
          </>
        )}
      </div>

      {/* Contact button skeleton */}
      <div className="mt-5">
        <Skeleton className="w-full h-12 rounded-md animate-pulse" />
      </div>
    </div>
  );
};

export default OwnerInformationSkeleton;
