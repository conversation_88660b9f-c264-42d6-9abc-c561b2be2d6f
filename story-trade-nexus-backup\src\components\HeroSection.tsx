
import React from 'react';
import { <PERSON>R<PERSON> } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from './ui/button-variants';
import SearchBar from './SearchBar';

interface HeroSectionProps {
  onSearch?: (query: string) => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onSearch }) => {
  return (
    <div className="relative bg-gradient-to-br from-beige-500 to-beige-100 pt-8 pb-24">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0"
             style={{
               backgroundImage: "url('data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M54.627 0l.83.828-1.415 1.415L51.8 0h2.827zM5.373 0l-.83.828L5.96 2.243 8.2 0H5.374zM48.97 0l3.657 3.657-1.414 1.414L46.143 0h2.828zM11.03 0L7.372 3.657 8.787 5.07 13.857 0H11.03zm32.284 0L49.8 6.485 48.384 7.9l-7.9-7.9h2.83zm-24.596 0L12.143 6.485l1.415 1.414 7.9-7.9h-2.83zM38.384 0l3.657 3.657-1.414 1.414-3.657-3.657H38.384zm-20.83 0l-3.657 3.657 1.415 1.414L19.17 0h-1.625zM33.84 0l3.658 3.657-1.414 1.414L30 0h3.84zM1.414 0L0 1.414l3.657 3.657 1.414-1.414L1.414 0zM56.97 0l-3.657 3.657 1.414 1.414L58.385 1.414 56.97 0z' fill='%239C92AC' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E')"
             }}
        ></div>
      </div>

      <div className="container mx-auto px-4 relative z-10 mt-8">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-playfair font-bold text-navy-800 mb-6">
            Share the <span className="text-burgundy-500">Joy of Reading</span> with Your Community
          </h1>
          <p className="text-lg md:text-xl text-gray-700 mb-8">
            Rent, buy, or exchange used books directly with other readers. No middleman, just a community of book lovers sharing stories.
          </p>

          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 justify-center mb-12">
            <Link to="/browse">
              <Button size="lg">
                Browse Books
              </Button>
            </Link>
            <Link to="/add-books">
              <Button variant="navy" size="lg">
                Add Your Books
              </Button>
            </Link>
          </div>

          <SearchBar className="max-w-2xl mx-auto" onSearch={onSearch} />
        </div>
      </div>

      <div className="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" className="fill-white">
          <path d="M0,64L48,58.7C96,53,192,43,288,48C384,53,480,75,576,80C672,85,768,75,864,69.3C960,64,1056,64,1152,58.7C1248,53,1344,43,1392,37.3L1440,32L1440,100L1392,100C1344,100,1248,100,1152,100C1056,100,960,100,864,100C768,100,672,100,576,100C480,100,384,100,288,100C192,100,96,100,48,100L0,100Z"></path>
        </svg>
      </div>
    </div>
  );
};

export default HeroSection;
