import { db, storage } from '../firebase/firebaseConfig';
import { collection, doc, addDoc, updateDoc, deleteDoc, getDocs, query, where, onSnapshot, getDoc } from 'firebase/firestore';
import { ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { v4 as uuidv4 } from 'uuid';

import type { BlogPost } from '../types/blog.d.ts';

// Auto-generate slug from title
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
};

// Upload cover image to Firebase Storage
export const uploadCoverImage = async (file: File): Promise<string> => {
  const storageRef = ref(storage, `blog-images/${uuidv4()}-${file.name}`);
  const snapshot = await uploadBytesResumable(storageRef, file);
  return await getDownloadURL(snapshot.ref);
};

// Create a new blog post
export const createBlogPost = async (postData: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  const blogPost = {
    ...postData,
    slug: generateSlug(postData.title),
    createdAt: new Date(),
    updatedAt: new Date(),
    draft: true
  };
  
  const docRef = await addDoc(collection(db, 'blogPosts'), blogPost);
  return docRef.id;
};

// Update existing blog post
export const updateBlogPost = async (id: string, updateData: Partial<BlogPost>): Promise<void> => {
  const postRef = doc(db, 'blogPosts', id);
  
  // Filter out empty tags
  const filteredUpdateData = {
    ...updateData,
    tags: updateData.tags?.filter(tag => tag.trim() !== '') || [],
    updatedAt: new Date()
  };
  
  await updateDoc(postRef, filteredUpdateData);
};

// Auto-save draft
export const autoSaveDraft = async (id: string, draftData: Partial<BlogPost>): Promise<void> => {
  const postRef = doc(db, 'blogPosts', id);
  
  const draftUpdate = {
    ...draftData,
    draft: true,
    updatedAt: new Date()
  };
  
  await updateDoc(postRef, draftUpdate);
};

// Delete blog post
export const deleteBlogPost = async (id: string): Promise<void> => {
  await deleteDoc(doc(db, 'blogPosts', id));
};

// Toggle publish status
export const togglePublishStatus = async (id: string, published: boolean): Promise<void> => {
  const postRef = doc(db, 'blogPosts', id);
  await updateDoc(postRef, { published, draft: !published });
};

// Get all blog posts (optionally filter by published status)
export const getAllBlogPosts = async (publishedOnly: boolean = false): Promise<BlogPost[]> => {
  const q = publishedOnly 
    ? query(collection(db, 'blogPosts'), where('published', '==', true))
    : collection(db, 'blogPosts');
  
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
    createdAt: doc.data().createdAt?.toDate(),
    updatedAt: doc.data().updatedAt?.toDate()
  })) as BlogPost[];
};

// Get blog post by slug
export const getBlogPostBySlug = async (slug: string): Promise<BlogPost | null> => {
  const q = query(collection(db, 'blogPosts'), where('slug', '==', slug));
  const snapshot = await getDocs(q);
  
  if (snapshot.empty) {
    return null;
  }
  
  const doc = snapshot.docs[0];
  return {
    id: doc.id,
    ...doc.data(),
    createdAt: doc.data().createdAt?.toDate(),
    updatedAt: doc.data().updatedAt?.toDate()
  } as BlogPost;
};

// Subscribe to blog posts updates
export const onBlogPostsUpdate = (callback: (posts: BlogPost[]) => void) => {
  const q = collection(db, 'blogPosts');
  return onSnapshot(q, snapshot => {
    const posts = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate()
    })) as BlogPost[];
    callback(posts);
  });
};

// Subscribe to a specific blog post
export const onBlogPostUpdate = (id: string, callback: (post: BlogPost) => void) => {
  const postRef = doc(db, 'blogPosts', id);
  return onSnapshot(postRef, doc => {
    if (doc.exists()) {
      callback({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      } as BlogPost);
    }
  });
};
