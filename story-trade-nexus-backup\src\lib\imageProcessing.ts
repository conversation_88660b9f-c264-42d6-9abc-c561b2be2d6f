/**
 * Image Processing Utilities for PeerBooks
 * 
 * This module provides client-side image processing capabilities including:
 * - WebP conversion for better performance
 * - Image optimization (resize, compress)
 * - Format validation and sanitization
 * - Browser compatibility checks
 */

// Image processing configuration
export const IMAGE_CONFIG = {
  // Maximum dimensions
  maxWidth: 1200,
  maxHeight: 1200,
  
  // Quality settings
  webpQuality: 0.85,
  jpegQuality: 0.85,
  
  // File size limits
  maxFileSize: 5 * 1024 * 1024, // 5MB
  targetFileSize: 200 * 1024,   // 200KB target
  
  // Supported formats
  supportedFormats: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  outputFormat: 'webp', // Default output format
  
  // Fallback format for browsers that don't support WebP
  fallbackFormat: 'jpeg'
};

/**
 * Check if the browser supports WebP format
 */
export function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // Check file type
  if (!IMAGE_CONFIG.supportedFormats.includes(file.type)) {
    return {
      valid: false,
      error: `Unsupported file format. Supported formats: ${IMAGE_CONFIG.supportedFormats.join(', ')}`
    };
  }
  
  // Check file size
  if (file.size > IMAGE_CONFIG.maxFileSize) {
    return {
      valid: false,
      error: `File size too large. Maximum size: ${IMAGE_CONFIG.maxFileSize / 1024 / 1024}MB`
    };
  }
  
  return { valid: true };
}

/**
 * Create a canvas element for image processing
 */
function createCanvas(width: number, height: number): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  return canvas;
}

/**
 * Calculate optimal dimensions while preserving aspect ratio
 */
function calculateDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number = IMAGE_CONFIG.maxWidth,
  maxHeight: number = IMAGE_CONFIG.maxHeight
): { width: number; height: number } {
  let { width, height } = { width: originalWidth, height: originalHeight };
  
  // Calculate scaling factor
  const scaleX = maxWidth / width;
  const scaleY = maxHeight / height;
  const scale = Math.min(scaleX, scaleY, 1); // Don't upscale
  
  width = Math.round(width * scale);
  height = Math.round(height * scale);
  
  return { width, height };
}

/**
 * Load image from file
 */
function loadImage(file: File): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Convert canvas to blob with specified format and quality
 */
function canvasToBlob(
  canvas: HTMLCanvasElement,
  format: string = 'webp',
  quality: number = 0.85
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to convert canvas to blob'));
        }
      },
      `image/${format}`,
      quality
    );
  });
}

/**
 * Process and optimize image
 */
export async function processImage(
  file: File,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
    targetSize?: number;
  } = {}
): Promise<{ file: File; originalSize: number; processedSize: number; format: string }> {
  // Validate input file
  const validation = validateImageFile(file);
  if (!validation.valid) {
    throw new Error(validation.error);
  }
  
  // Set processing options
  const {
    maxWidth = IMAGE_CONFIG.maxWidth,
    maxHeight = IMAGE_CONFIG.maxHeight,
    quality = IMAGE_CONFIG.webpQuality,
    format = (await supportsWebP()) ? 'webp' : IMAGE_CONFIG.fallbackFormat,
    targetSize = IMAGE_CONFIG.targetFileSize
  } = options;
  
  try {
    // Load the image
    const img = await loadImage(file);
    
    // Calculate optimal dimensions
    const { width, height } = calculateDimensions(img.naturalWidth, img.naturalHeight, maxWidth, maxHeight);
    
    // Create canvas and draw resized image
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }
    
    // Enable image smoothing for better quality
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    // Draw the image
    ctx.drawImage(img, 0, 0, width, height);
    
    // Clean up object URL
    URL.revokeObjectURL(img.src);
    
    // Convert to blob with optimization
    let processedBlob = await canvasToBlob(canvas, format, quality);
    
    // If the file is still too large, reduce quality iteratively
    let currentQuality = quality;
    while (processedBlob.size > targetSize && currentQuality > 0.3) {
      currentQuality -= 0.1;
      processedBlob = await canvasToBlob(canvas, format, currentQuality);
    }
    
    // Create processed file
    const processedFile = new File(
      [processedBlob],
      `${file.name.split('.')[0]}.${format}`,
      { type: `image/${format}` }
    );
    
    return {
      file: processedFile,
      originalSize: file.size,
      processedSize: processedFile.size,
      format
    };
    
  } catch (error) {
    console.error('Image processing failed:', error);
    throw new Error(`Image processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Process multiple images
 */
export async function processMultipleImages(
  files: File[],
  options: Parameters<typeof processImage>[1] = {},
  onProgress?: (progress: number, currentFile: string) => void
): Promise<Array<{ file: File; originalSize: number; processedSize: number; format: string }>> {
  const results = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    
    if (onProgress) {
      onProgress((i / files.length) * 100, file.name);
    }
    
    try {
      const result = await processImage(file, options);
      results.push(result);
    } catch (error) {
      console.error(`Failed to process ${file.name}:`, error);
      // Continue with other files, but log the error
      throw error;
    }
  }
  
  if (onProgress) {
    onProgress(100, 'Complete');
  }
  
  return results;
}

/**
 * Generate image preview URL
 */
export function generatePreviewUrl(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Clean up preview URL
 */
export function cleanupPreviewUrl(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * Get image metadata
 */
export async function getImageMetadata(file: File): Promise<{
  width: number;
  height: number;
  size: number;
  type: string;
  name: string;
}> {
  const img = await loadImage(file);
  
  return {
    width: img.naturalWidth,
    height: img.naturalHeight,
    size: file.size,
    type: file.type,
    name: file.name
  };
}
