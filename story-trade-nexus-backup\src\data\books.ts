
import { Book } from '../types/index';

export const books: Book[] = [
  {
    id: '1',
    title: 'To Kill a Mockingbird',
    author: '<PERSON>',
    isbn: '9780061120084',
    genre: ['Fiction', 'Classics', 'Literature'],
    condition: 'Good',
    description: 'A classic novel about racial injustice in the American South through the eyes of a young girl.',
    imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1553383690i/2657.jpg',
    perceivedValue: 8,
    price: 350,
    rentalPrice: 50,
    rentalPeriod: 'per week',
    availability: 'For Rent, Sale & Exchange',
    ownerId: 'user1',
    ownerName: '<PERSON>',
    ownerLocation: 'Mumbai',
    ownerRating: 4.8,
    distance: 3.2,
    createdAt: new Date('2023-05-15')
  },
  {
    id: '2',
    title: '1984',
    author: '<PERSON>',
    isbn: '9780451524935',
    genre: ['Fiction', 'Classics', 'Dystopian'],
    condition: 'Like New',
    description: 'A dystopian novel set in a totalitarian society where critical thought is suppressed.',
    imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1657781256i/61439040.jpg',
    perceivedValue: 9,
    price: 400,
    availability: 'For Sale & Exchange',
    ownerId: 'user2',
    ownerName: 'Raj Patel',
    ownerLocation: 'Delhi',
    ownerRating: 4.5,
    distance: 5.7,
    createdAt: new Date('2023-06-22')
  },
  {
    id: '3',
    title: 'The Great Gatsby',
    author: 'F. Scott Fitzgerald',
    isbn: '9780743273565',
    genre: ['Fiction', 'Classics', 'Literature'],
    condition: 'Good',
    description: 'A story of wealth, love, and the American Dream in the 1920s.',
    imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1490528560i/4671.jpg',
    perceivedValue: 7,
    rentalPrice: 40,
    rentalPeriod: 'per week',
    availability: 'For Rent & Exchange',
    ownerId: 'user3',
    ownerName: 'Anita Mehta',
    ownerLocation: 'Bangalore',
    ownerRating: 4.9,
    distance: 1.3,
    createdAt: new Date('2023-03-11')
  },
  {
    id: '4',
    title: 'Harry Potter and the Sorcerer\'s Stone',
    author: 'J.K. Rowling',
    isbn: '9780590353427',
    genre: ['Fantasy', 'Young Adult', 'Fiction'],
    condition: 'Fair',
    description: 'The first book in the beloved Harry Potter series about a boy who discovers he\'s a wizard.',
    imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1474154022i/3.jpg',
    perceivedValue: 6,
    price: 300,
    rentalPrice: 35,
    rentalPeriod: 'per week',
    availability: 'For Rent & Sale',
    ownerId: 'user4',
    ownerName: 'Vikram Singh',
    ownerLocation: 'Chennai',
    ownerRating: 4.6,
    distance: 4.5,
    createdAt: new Date('2023-07-05')
  },
  {
    id: '5',
    title: 'The Alchemist',
    author: 'Paulo Coelho',
    isbn: '9780062315007',
    genre: ['Fiction', 'Philosophy', 'Fantasy'],
    condition: 'New',
    description: 'A philosophical novel about a young shepherd who dreams of finding treasure and embarks on a journey.',
    imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1654371463i/18144590.jpg',
    perceivedValue: 9,
    price: 450,
    availability: 'For Exchange',
    ownerId: 'user5',
    ownerName: 'Priya Sharma',
    ownerLocation: 'Kolkata',
    ownerRating: 4.7,
    distance: 6.8,
    createdAt: new Date('2023-08-19')
  },
  {
    id: '6',
    title: 'Pride and Prejudice',
    author: 'Jane Austen',
    isbn: '9780141439518',
    genre: ['Fiction', 'Classics', 'Romance'],
    condition: 'Like New',
    description: 'A classic romance novel examining the relationships between young people in 19th century England.',
    imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1320399351i/1885.jpg',
    perceivedValue: 8,
    rentalPrice: 45,
    rentalPeriod: 'per week',
    availability: 'For Rent & Exchange',
    ownerId: 'user6',
    ownerName: 'Anjali Kumar',
    ownerLocation: 'Hyderabad',
    ownerRating: 4.4,
    distance: 2.9,
    createdAt: new Date('2023-04-30')
  }
];
