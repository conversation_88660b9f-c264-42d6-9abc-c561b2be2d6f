import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Mail, Phone, MessageSquare, RefreshCw, Eye, EyeOff, Loader2 } from 'lucide-react';
import { useAuth } from '@/lib/AuthContext';
import { ContactMessage, getAllContactMessages, markMessageAsRead, markMessageAsUnread } from '@/lib/contactMessageService';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import AdminLayout from '@/components/layout/AdminLayout';

const AdminContactMessages: React.FC = () => {
  const { currentUser } = useAuth();
  const [messages, setMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMessage, setSelectedMessage] = useState<ContactMessage | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isMarkingAsRead, setIsMarkingAsRead] = useState(false);
  
  // Load messages on component mount
  useEffect(() => {
    loadMessages();
  }, []);
  
  // Function to load messages from Firestore
  const loadMessages = async () => {
    try {
      setLoading(true);
      const fetchedMessages = await getAllContactMessages();
      setMessages(fetchedMessages);
    } catch (error) {
      console.error('Error loading contact messages:', error);
      toast({
        title: 'Error',
        description: 'Failed to load contact messages. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Function to handle marking a message as read
  const handleMarkAsRead = async (messageId: string) => {
    try {
      setIsMarkingAsRead(true);
      await markMessageAsRead(messageId);
      
      // Update the messages state
      setMessages(messages.map(message => 
        message.id === messageId 
          ? { ...message, isRead: true, readAt: new Date() } 
          : message
      ));
      
      // Update the selected message if it's open in the dialog
      if (selectedMessage && selectedMessage.id === messageId) {
        setSelectedMessage({ ...selectedMessage, isRead: true, readAt: new Date() });
      }
      
      toast({
        title: 'Success',
        description: 'Message marked as read.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error marking message as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark message as read. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsMarkingAsRead(false);
    }
  };
  
  // Function to handle marking a message as unread
  const handleMarkAsUnread = async (messageId: string) => {
    try {
      setIsMarkingAsRead(true);
      await markMessageAsUnread(messageId);
      
      // Update the messages state
      setMessages(messages.map(message => 
        message.id === messageId 
          ? { ...message, isRead: false, readAt: null } 
          : message
      ));
      
      // Update the selected message if it's open in the dialog
      if (selectedMessage && selectedMessage.id === messageId) {
        setSelectedMessage({ ...selectedMessage, isRead: false, readAt: null });
      }
      
      toast({
        title: 'Success',
        description: 'Message marked as unread.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error marking message as unread:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark message as unread. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsMarkingAsRead(false);
    }
  };
  
  // Function to open the message dialog
  const openMessageDialog = (message: ContactMessage) => {
    setSelectedMessage(message);
    setIsDialogOpen(true);
    
    // If the message is unread, mark it as read
    if (!message.isRead && message.id) {
      handleMarkAsRead(message.id);
    }
  };
  
  // Function to close the message dialog
  const closeMessageDialog = () => {
    setIsDialogOpen(false);
    setSelectedMessage(null);
  };
  
  // Function to format the timestamp
  const formatTimestamp = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    
    // If it's a Firestore timestamp, convert to JS Date
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return format(date, 'MMM d, yyyy h:mm a');
  };
  
  // Count unread messages
  const unreadCount = messages.filter(message => !message.isRead).length;

  return (
    <AdminLayout title="Contact Messages" description="View and manage contact messages from users">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-navy-800 mb-2">Contact Messages</h1>
          <p className="text-gray-600">
            View and respond to messages from users
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount} unread
              </Badge>
            )}
          </p>
        </div>
        
        <Button 
          onClick={loadMessages} 
          variant="outline" 
          className="mt-4 md:mt-0"
          disabled={loading}
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Loading...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-burgundy-500" />
          <span className="ml-2 text-gray-600">Loading messages...</span>
        </div>
      ) : messages.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">No Messages Yet</h2>
          <p className="text-gray-600">
            There are no contact messages from users yet. Messages will appear here when users send them through the Contact Us page.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {messages.map((message) => (
            <div 
              key={message.id} 
              className={`bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer ${!message.isRead ? 'border-l-4 border-burgundy-500' : ''}`}
              onClick={() => openMessageDialog(message)}
            >
              <div className="flex flex-col md:flex-row justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <Mail className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-navy-800 font-medium">{message.email}</span>
                    {!message.isRead && (
                      <Badge variant="default" className="ml-2">New</Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center mb-2">
                    <Phone className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-gray-600">{message.phone}</span>
                  </div>
                  
                  <p className="text-gray-700 line-clamp-2 mb-2">
                    {message.message}
                  </p>
                  
                  <div className="text-xs text-gray-500">
                    Received: {formatTimestamp(message.createdAt)}
                  </div>
                </div>
                
                <div className="mt-4 md:mt-0 md:ml-4 flex items-center">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={(e) => {
                      e.stopPropagation();
                      if (message.id) {
                        message.isRead 
                          ? handleMarkAsUnread(message.id) 
                          : handleMarkAsRead(message.id);
                      }
                    }}
                    disabled={isMarkingAsRead}
                  >
                    {message.isRead ? (
                      <>
                        <EyeOff className="h-4 w-4 mr-2" />
                        Mark as unread
                      </>
                    ) : (
                      <>
                        <Eye className="h-4 w-4 mr-2" />
                        Mark as read
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* Message Detail Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={closeMessageDialog}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Message Details</DialogTitle>
            <DialogDescription>
              Contact message from user
            </DialogDescription>
          </DialogHeader>
          
          {selectedMessage && (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4 py-2 border-b">
                <div className="font-medium">From:</div>
                <div className="col-span-2">{selectedMessage.email}</div>
              </div>
              
              <div className="grid grid-cols-3 gap-4 py-2 border-b">
                <div className="font-medium">Phone:</div>
                <div className="col-span-2">{selectedMessage.phone}</div>
              </div>
              
              <div className="grid grid-cols-3 gap-4 py-2 border-b">
                <div className="font-medium">Received:</div>
                <div className="col-span-2">{formatTimestamp(selectedMessage.createdAt)}</div>
              </div>
              
              <div className="grid grid-cols-3 gap-4 py-2 border-b">
                <div className="font-medium">Status:</div>
                <div className="col-span-2">
                  {selectedMessage.isRead ? (
                    <span className="text-green-600 flex items-center">
                      <Eye className="h-4 w-4 mr-1" />
                      Read {selectedMessage.readAt && `(${formatTimestamp(selectedMessage.readAt)})`}
                    </span>
                  ) : (
                    <span className="text-burgundy-600 flex items-center">
                      <EyeOff className="h-4 w-4 mr-1" />
                      Unread
                    </span>
                  )}
                </div>
              </div>
              
              <div className="py-2">
                <div className="font-medium mb-2">Message:</div>
                <div className="bg-gray-50 p-4 rounded-md whitespace-pre-wrap">
                  {selectedMessage.message}
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button 
                  variant="outline" 
                  onClick={closeMessageDialog}
                >
                  Close
                </Button>
                
                <Button 
                  variant={selectedMessage.isRead ? "outline" : "default"}
                  onClick={() => {
                    if (selectedMessage.id) {
                      selectedMessage.isRead 
                        ? handleMarkAsUnread(selectedMessage.id) 
                        : handleMarkAsRead(selectedMessage.id);
                    }
                  }}
                  disabled={isMarkingAsRead}
                >
                  {isMarkingAsRead ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : selectedMessage.isRead ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-2" />
                      Mark as unread
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      Mark as read
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminContactMessages;
