import React from "react";
import { Link } from "react-router-dom";
import { Mail, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button-variants";
import { useAuth } from "@/lib/AuthContext";
import { toast } from "sonner";

interface EmailVerificationRequiredProps {
  featureName: string;
  message?: string;
}

/**
 * Component shown to users who need to verify their email before accessing certain features
 */
const EmailVerificationRequired: React.FC<EmailVerificationRequiredProps> = ({
  featureName,
  message,
}) => {
  const { currentUser, sendVerificationEmail } = useAuth();
  const [isLoading, setIsLoading] = React.useState(false);

  const handleResendEmail = async () => {
    if (!currentUser) return;

    setIsLoading(true);
    try {
      await sendVerificationEmail();
      toast.success("Verification email sent! Please check your inbox.");
    } catch (error) {
      console.error("Error sending verification email:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to send verification email";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-8 max-w-md mx-auto">
      <div className="text-center">
        <div className="mx-auto bg-amber-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4">
          <AlertTriangle className="h-8 w-8 text-amber-600" />
        </div>
        <h1 className="text-2xl font-bold text-navy-800 font-playfair mb-2">
          Email Verification Required
        </h1>
        <p className="text-gray-600 mb-4">
          {message || `You need to verify your email address before you can ${featureName}.`}
        </p>

        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
          <p className="text-blue-800 text-sm">
            <strong>Why verify?</strong> Email verification helps ensure the security of your account
            and allows us to contact you about your books and transactions.
          </p>
          <p className="text-blue-700 text-sm mt-2">
            <strong>While waiting for verification:</strong> You can still browse books, view book details,
            and access public pages, but you won't be able to add books or access your dashboard until
            your email is verified.
          </p>
        </div>

        <div className="space-y-4">
          <Button
            onClick={handleResendEmail}
            disabled={isLoading}
            className="w-full flex items-center justify-center gap-2"
          >
            <Mail className="h-4 w-4" />
            {isLoading ? "Sending..." : "Resend Verification Email"}
          </Button>

          <Link to="/verify-email">
            <Button
              variant="outline"
              className="w-full"
            >
              Go to Verification Page
            </Button>
          </Link>
        </div>

        <div className="mt-6 text-sm text-gray-500">
          <p>
            Already verified your email? Try refreshing the page or signing out and signing back in.
          </p>
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationRequired;
