// Script to list all books in Firestore
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDXQnxRLuSWnhj9QkO-UGOGbxJz0Gqfzz0",
  authDomain: "book-share-98f6a.firebaseapp.com",
  projectId: "book-share-98f6a",
  storageBucket: "book-share-98f6a.appspot.com",
  messagingSenderId: "1051281275473",
  appId: "1:1051281275473:web:c6e3a7d3d1a1de2e3a8f9c"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Function to list all books
async function listAllBooks() {
  try {
    console.log('Listing all books in Firestore');
    
    // Get all books from Firestore
    const booksRef = collection(db, 'books');
    const booksSnapshot = await getDocs(booksRef);
    
    if (booksSnapshot.empty) {
      console.log('No books found in Firestore');
      return;
    }
    
    console.log(`Found ${booksSnapshot.size} books in Firestore`);
    
    // Loop through all books
    booksSnapshot.forEach((doc) => {
      const data = doc.data();
      
      console.log(`Book ID: ${doc.id}, Title: ${data.title}`);
      
      // Check if the book is the one we're looking for
      if (data.title && data.title.includes('Mystery Of The Missing Cat')) {
        console.log('FOUND TARGET BOOK: Mystery Of The Missing Cat');
        console.log('Book ID:', doc.id);
        console.log('Book data:', data);
        console.log('Security deposit details:', {
          required: data.securityDepositRequired,
          amount: data.securityDepositAmount,
          types: {
            required: typeof data.securityDepositRequired,
            amount: typeof data.securityDepositAmount
          }
        });
      }
      
      // Check if the book has rental price and security deposit
      if (data.rentalPrice && (data.securityDepositRequired || data.securityDepositAmount)) {
        console.log('FOUND BOOK WITH SECURITY DEPOSIT:');
        console.log('Book ID:', doc.id);
        console.log('Book Title:', data.title);
        console.log('Security deposit details:', {
          required: data.securityDepositRequired,
          amount: data.securityDepositAmount,
          types: {
            required: typeof data.securityDepositRequired,
            amount: typeof data.securityDepositAmount
          }
        });
      }
    });
  } catch (error) {
    console.error('Error listing books:', error);
  }
}

// List all books
listAllBooks();
