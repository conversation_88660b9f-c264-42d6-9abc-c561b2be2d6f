/**
 * <PERSON><PERSON><PERSON> to update all books with location information based on pincodes
 */

import { updateBooksWithPincodeLocations } from '../utils/updateBookLocationsFromPincodes';

// Self-executing async function
(async () => {
  console.log('Starting book location update script...');
  
  try {
    const result = await updateBooksWithPincodeLocations();
    
    if (result.success) {
      console.log('✅ Success!');
      console.log(`Updated ${result.updatedCount} books with location information`);
      console.log(`Errors: ${result.errorCount}`);
      
      if (result.updatedBooks.length > 0) {
        console.log('Updated books:');
        result.updatedBooks.forEach(bookId => console.log(`- ${bookId}`));
      }
      
      if (result.failedBooks.length > 0) {
        console.log('Failed books:');
        result.failedBooks.forEach(bookId => console.log(`- ${bookId}`));
      }
    } else {
      console.error('❌ Failed to update book locations');
      console.error(result.message);
    }
  } catch (error) {
    console.error('❌ Error running update script:', error);
  }
  
  // Force exit after completion
  process.exit(0);
})();
