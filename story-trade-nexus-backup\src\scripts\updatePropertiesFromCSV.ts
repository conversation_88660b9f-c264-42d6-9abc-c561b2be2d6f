/**
 * <PERSON><PERSON><PERSON> to update the existing Firebase Firestore collection 'hyderabadProperties'
 * with new property data from CSV file
 *
 * This script reads and parses CSV files containing property data
 * and updates the existing Firestore collection with new property records.
 *
 * Features:
 * - Reads and validates CSV data from configurable file path
 * - Handles data conflicts with merge strategy
 * - Uses batch operations for efficient writes
 * - Includes comprehensive error handling and logging
 * - Supports dry-run mode for preview
 * - Validates data integrity before writing
 * - Supports any valid Indian pincode (6 digits)
 *
 * Usage:
 * - Dry run: npm run update:properties:dry -- --csv-file=my-data.csv
 * - Live run: npm run update:properties -- --csv-file=my-data.csv
 * - Default: npm run update:properties (uses hyderabad_properties_500010-500040.csv)
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */

import fs from 'fs';
import { parse } from 'csv-parse';
import path from 'path';
import { initializeFirebase, db } from '../lib/firebase';

// TypeScript interfaces
interface CSVRow {
  communityname: string;
  address: string;
  pincode: string;
  latitude: string;
  longitude: string;
}

interface HyderabadPropertyDocument {
  communityName: string;
  address: string;
  pincode: number | string;
  location: {
    latitude: number;
    longitude: number;
  };
  geoPoint: {
    latitude: number;
    longitude: number;
  };
  createdAt: Date;
  updatedAt: Date;
  communityNameLower: string;
  imported: boolean;
  source?: string;
  batchId?: string;
}

interface UpdateResult {
  success: boolean;
  totalProcessed: number;
  newRecords: number;
  updatedRecords: number;
  errorCount: number;
  errors: string[];
  skippedRecords: number;
  message: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Configuration constants
const DEFAULT_CSV_FILENAME = 'hyderabad_properties_500010-500040.csv';
const BATCH_SIZE = 500; // Firestore batch limit
const COLLECTION_NAME = 'hyderabadProperties';
const REQUIRED_FIELDS = ['communityname', 'address', 'pincode', 'latitude', 'longitude'];

// Command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const batchSizeArg = args.find(arg => arg.startsWith('--batch-size='));
const csvFileArg = args.find(arg => arg.startsWith('--csv-file=') || arg.startsWith('--input='));
const customBatchSize = batchSizeArg ? parseInt(batchSizeArg.split('=')[1]) : BATCH_SIZE;

// Determine CSV file path
let csvFileName = DEFAULT_CSV_FILENAME;
if (csvFileArg) {
  csvFileName = csvFileArg.split('=')[1];
}
const CSV_FILE_PATH = path.resolve(process.cwd(), csvFileName);

// Logger utility
const logger = {
  info: (message: string) => console.log(`[INFO] ${new Date().toISOString()}: ${message}`),
  success: (message: string) => console.log(`[SUCCESS] ${new Date().toISOString()}: ${message}`),
  error: (message: string, error?: any) => console.error(`[ERROR] ${new Date().toISOString()}: ${message}`, error || ''),
  warning: (message: string) => console.warn(`[WARNING] ${new Date().toISOString()}: ${message}`),
  debug: (message: string) => console.log(`[DEBUG] ${new Date().toISOString()}: ${message}`)
};

/**
 * Validates that a CSV row has all required fields and correct data types
 */
function validateCSVRow(row: CSVRow, rowIndex: number): ValidationResult {
  const errors: string[] = [];

  // Check for required fields
  for (const field of REQUIRED_FIELDS) {
    if (!row[field as keyof CSVRow] || row[field as keyof CSVRow].trim() === '') {
      errors.push(`Row ${rowIndex}: Missing required field '${field}'`);
    }
  }

  // Validate pincode format (Indian pincodes are 6 digits)
  if (row.pincode && !/^\d{6}$/.test(row.pincode.trim())) {
    errors.push(`Row ${rowIndex}: Invalid pincode format '${row.pincode}' (must be 6 digits)`);
  }

  // Validate latitude and longitude are valid numbers
  const lat = parseFloat(row.latitude);
  const lng = parseFloat(row.longitude);

  if (isNaN(lat) || lat < -90 || lat > 90) {
    errors.push(`Row ${rowIndex}: Invalid latitude '${row.latitude}' (must be between -90 and 90)`);
  }

  if (isNaN(lng) || lng < -180 || lng > 180) {
    errors.push(`Row ${rowIndex}: Invalid longitude '${row.longitude}' (must be between -180 and 180)`);
  }

  // Validate community name length
  if (row.communityname && row.communityname.trim().length > 200) {
    errors.push(`Row ${rowIndex}: Community name too long (max 200 characters)`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Transforms a CSV row into a Firestore document
 */
function transformCSVRowToDocument(row: CSVRow, batchId: string): HyderabadPropertyDocument {
  const latitude = parseFloat(row.latitude);
  const longitude = parseFloat(row.longitude);

  // Convert pincode to number if possible, otherwise keep as string
  let pincode: number | string = parseInt(row.pincode.trim());
  if (isNaN(pincode)) {
    pincode = row.pincode.trim();
  }

  return {
    communityName: row.communityname.trim(),
    address: row.address.trim(),
    pincode: pincode,
    location: {
      latitude: latitude,
      longitude: longitude
    },
    geoPoint: {
      latitude: latitude,
      longitude: longitude
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    communityNameLower: row.communityname.trim().toLowerCase(),
    imported: true,
    source: csvFileName,
    batchId: batchId
  };
}

/**
 * Checks if a document with similar data already exists in Firestore
 */
async function checkForExistingDocument(document: HyderabadPropertyDocument): Promise<boolean> {
  try {
    const { collection, query, where, getDocs, limit } = await import('firebase/firestore');

    // Query for documents with same community name and pincode
    const existingQuery = query(
      collection(db, COLLECTION_NAME),
      where('communityNameLower', '==', document.communityNameLower),
      where('pincode', '==', document.pincode),
      limit(1)
    );

    const snapshot = await getDocs(existingQuery);
    return !snapshot.empty;
  } catch (error) {
    logger.warning(`Error checking for existing document: ${error}`);
    return false;
  }
}

/**
 * Reads and parses the CSV file
 */
async function readCSVFile(): Promise<CSVRow[]> {
  return new Promise((resolve, reject) => {
    const records: CSVRow[] = [];

    if (!fs.existsSync(CSV_FILE_PATH)) {
      reject(new Error(`CSV file not found: ${CSV_FILE_PATH}`));
      return;
    }

    logger.info(`Reading CSV file: ${CSV_FILE_PATH}`);

    const fileStream = fs.createReadStream(CSV_FILE_PATH);
    const parser = parse({
      columns: true,
      skip_empty_lines: true,
      trim: true
    });

    fileStream
      .pipe(parser)
      .on('data', (row: CSVRow) => {
        records.push(row);
      })
      .on('end', () => {
        logger.success(`Successfully read ${records.length} records from CSV`);
        resolve(records);
      })
      .on('error', (error) => {
        logger.error('Error reading CSV file:', error);
        reject(error);
      });
  });
}

/**
 * Processes records in batches and updates Firestore
 */
async function processBatch(
  documents: HyderabadPropertyDocument[],
  batchNumber: number,
  isDryRun: boolean
): Promise<{ processed: number; errors: string[] }> {
  const errors: string[] = [];
  let processed = 0;

  if (isDryRun) {
    logger.info(`[DRY RUN] Would process batch ${batchNumber} with ${documents.length} documents`);
    return { processed: documents.length, errors };
  }

  try {
    const { writeBatch, collection, doc } = await import('firebase/firestore');

    const batch = writeBatch(db);
    const collectionRef = collection(db, COLLECTION_NAME);

    for (const document of documents) {
      try {
        // Check if document already exists
        const exists = await checkForExistingDocument(document);

        if (exists) {
          logger.debug(`Document already exists for ${document.communityName} in ${document.pincode}, skipping`);
          continue;
        }

        const docRef = doc(collectionRef); // Auto-generate ID
        batch.set(docRef, document);
        processed++;
      } catch (error) {
        const errorMsg = `Error processing document ${document.communityName}: ${error}`;
        errors.push(errorMsg);
        logger.error(errorMsg);
      }
    }

    if (processed > 0) {
      await batch.commit();
      logger.success(`Batch ${batchNumber} committed successfully with ${processed} documents`);
    } else {
      logger.info(`Batch ${batchNumber} had no new documents to add`);
    }

  } catch (error) {
    const errorMsg = `Error committing batch ${batchNumber}: ${error}`;
    errors.push(errorMsg);
    logger.error(errorMsg);
  }

  return { processed, errors };
}

/**
 * Main function to update Hyderabad properties from CSV
 */
async function updateHyderabadPropertiesFromCSV(): Promise<UpdateResult> {
  const startTime = Date.now();
  const batchId = `batch_${Date.now()}`;

  logger.info('Starting Hyderabad properties update process...');

  if (isDryRun) {
    logger.info('🔍 Running in DRY RUN mode - no actual database changes will be made');
  }

  logger.info(`Using batch size: ${customBatchSize}`);

  try {
    // Initialize Firebase
    await initializeFirebase();
    logger.success('Firebase initialized successfully');

    // Read and parse CSV file
    const csvRecords = await readCSVFile();

    if (csvRecords.length === 0) {
      return {
        success: false,
        totalProcessed: 0,
        newRecords: 0,
        updatedRecords: 0,
        errorCount: 0,
        errors: [],
        skippedRecords: 0,
        message: 'No records found in CSV file'
      };
    }

    // Validate all records first
    logger.info('Validating CSV records...');
    const validRecords: CSVRow[] = [];
    const validationErrors: string[] = [];
    let skippedRecords = 0;

    csvRecords.forEach((row, index) => {
      const validation = validateCSVRow(row, index + 1);
      if (validation.isValid) {
        validRecords.push(row);
      } else {
        validationErrors.push(...validation.errors);
        skippedRecords++;
      }
    });

    logger.info(`Validation complete: ${validRecords.length} valid, ${skippedRecords} invalid records`);

    if (validationErrors.length > 0) {
      logger.warning(`Found ${validationErrors.length} validation errors:`);
      validationErrors.forEach(error => logger.warning(error));
    }

    if (validRecords.length === 0) {
      return {
        success: false,
        totalProcessed: 0,
        newRecords: 0,
        updatedRecords: 0,
        errorCount: validationErrors.length,
        errors: validationErrors,
        skippedRecords,
        message: 'No valid records to process'
      };
    }

    // Transform valid records to Firestore documents
    logger.info('Transforming records to Firestore documents...');
    const documents = validRecords.map(row => transformCSVRowToDocument(row, batchId));

    // Process documents in batches
    logger.info(`Processing ${documents.length} documents in batches of ${customBatchSize}...`);

    let totalProcessed = 0;
    let totalErrors: string[] = [...validationErrors];
    let batchNumber = 1;

    for (let i = 0; i < documents.length; i += customBatchSize) {
      const batchDocuments = documents.slice(i, i + customBatchSize);

      logger.info(`Processing batch ${batchNumber}/${Math.ceil(documents.length / customBatchSize)} (${batchDocuments.length} documents)`);

      const batchResult = await processBatch(batchDocuments, batchNumber, isDryRun);
      totalProcessed += batchResult.processed;
      totalErrors.push(...batchResult.errors);

      batchNumber++;

      // Add delay between batches to avoid overwhelming Firestore
      if (i + customBatchSize < documents.length && !isDryRun) {
        logger.debug('Adding delay between batches...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    const result: UpdateResult = {
      success: true,
      totalProcessed: validRecords.length,
      newRecords: totalProcessed,
      updatedRecords: 0, // This implementation adds new records, doesn't update existing ones
      errorCount: totalErrors.length,
      errors: totalErrors,
      skippedRecords,
      message: isDryRun
        ? `Dry run completed in ${duration}s. Would process ${totalProcessed} new records.`
        : `Successfully processed ${totalProcessed} new records in ${duration}s. ${skippedRecords} records skipped due to validation errors.`
    };

    // Print summary
    logger.info('\n===== Update Summary =====');
    logger.success(`Total CSV records read: ${csvRecords.length}`);
    logger.success(`Valid records: ${validRecords.length}`);
    logger.success(`Records skipped (validation errors): ${skippedRecords}`);

    if (isDryRun) {
      logger.info(`[DRY RUN] Would add ${totalProcessed} new records to Firestore`);
    } else {
      logger.success(`New records added to Firestore: ${totalProcessed}`);
    }

    if (totalErrors.length > 0) {
      logger.warning(`Total errors encountered: ${totalErrors.length}`);
    } else {
      logger.success('No errors encountered during processing');
    }

    logger.success(`Processing completed in ${duration} seconds`);

    return result;

  } catch (error: any) {
    logger.error('Unhandled error during update process:', error);
    return {
      success: false,
      totalProcessed: 0,
      newRecords: 0,
      updatedRecords: 0,
      errorCount: 1,
      errors: [`Unhandled error: ${error.message}`],
      skippedRecords: 0,
      message: `Update process failed: ${error.message}`
    };
  }
}

/**
 * Display help information
 */
function displayHelp() {
  console.log(`
🏢 Properties Update Script
===========================

This script updates the Firebase Firestore 'hyderabadProperties' collection
with new property data from CSV files.

Usage:
  npm run update:properties [options]
  npx tsx src/scripts/updatePropertiesFromCSV.ts [options]

Options:
  --csv-file=<path>      Path to CSV file (default: hyderabad_properties_500010-500040.csv)
  --input=<path>         Alternative syntax for CSV file path
  --dry-run              Preview changes without updating the database
  --batch-size=<number>  Set custom batch size (default: 500, max: 500)
  --help                 Display this help message

Examples:
  # Dry run with default CSV file
  npm run update:properties:dry

  # Live run with custom CSV file
  npm run update:properties -- --csv-file=my-properties.csv

  # Dry run with custom file and batch size
  npm run update:properties:dry -- --csv-file=data.csv --batch-size=250

  # Direct execution with custom file
  npx tsx src/scripts/updatePropertiesFromCSV.ts --csv-file=custom.csv --dry-run

Features:
  ✅ Configurable CSV file input
  ✅ Validates CSV data integrity
  ✅ Prevents duplicate entries
  ✅ Batch processing for efficiency
  ✅ Comprehensive error handling
  ✅ Progress reporting
  ✅ Dry-run mode for safety

CSV File Requirements:
  - Location: Project root directory (or specify full path)
  - Columns: communityname, address, pincode, latitude, longitude
  - Pincode: 6-digit Indian postal codes
  - Coordinates: Valid latitude (-90 to 90) and longitude (-180 to 180)

For more information, see the project documentation.
`);
}

// Main execution
if (args.includes('--help')) {
  displayHelp();
  process.exit(0);
}

// Self-executing async function
(async () => {
  try {
    const result = await updateHyderabadPropertiesFromCSV();

    if (result.success) {
      logger.success('✅ Update process completed successfully!');
      if (result.errorCount > 0) {
        logger.warning(`⚠️  ${result.errorCount} errors occurred during processing`);
        process.exit(1);
      }
    } else {
      logger.error('❌ Update process failed');
      logger.error(result.message);
      process.exit(1);
    }
  } catch (error) {
    logger.error('❌ Unhandled error during script execution:', error);
    process.exit(1);
  }

  // Force exit after completion
  process.exit(0);
})();
