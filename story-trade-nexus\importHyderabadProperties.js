/**
 * <PERSON><PERSON>t to import Hyderabad properties data from CSV to Firebase Firestore
 *
 * This script reads the "hyderabad_properties - google.csv" file and imports
 * the data into a new Firestore collection named "hyderabadProperties".
 *
 * The script handles data transformation, batch processing, and error handling
 * to ensure a smooth import process.
 *
 * Usage: node importHyderabadProperties.js
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

// Import required modules
import fs from 'fs';
import { parse } from 'csv-parse';
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, writeBatch, doc } from 'firebase/firestore';
import path from 'path';

// Process command line arguments
const args = process.argv.slice(2);
const batchSizeArg = args.find(arg => arg.startsWith('--batch-size='));
const DEFAULT_BATCH_SIZE = 500;

// Create a logger utility
const logger = {
  info: (message) => console.log(`[INFO] ${new Date().toISOString()}: ${message}`),
  error: (message, error) => console.error(`[ERROR] ${new Date().toISOString()}: ${message}`, error || ''),
  success: (message) => console.log(`[SUCCESS] ${new Date().toISOString()}: ${message}`),
  warning: (message) => console.warn(`[WARNING] ${new Date().toISOString()}: ${message}`),
};

// Firebase configuration (same as in the application)
const firebaseConfig = {
  apiKey: "AIzaSyAgXbuXyBEa9aEPVB4JtLugCbRWK39Vj_c",
  authDomain: "book-share-98f6a.firebaseapp.com",
  projectId: "book-share-98f6a",
  storageBucket: "book-share-98f6a.firebasestorage.app",
  messagingSenderId: "216941059965",
  appId: "1:216941059965:web:2e0528a8a018ff959c7614",
  measurementId: "G-NYSPR3K1PY"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Path to the CSV file - using path.resolve for cross-platform compatibility
const csvFilePath = path.resolve(process.cwd(), 'hyderabad_properties - google.csv');

// Counter for imported records
let importedCount = 0;
let errorCount = 0;

/**
 * Validates that a row has all required fields
 * @param {Object} row - The CSV row as an object
 * @returns {boolean} - True if the row is valid, false otherwise
 */
function validateRow(row) {
  // Check for required fields
  const requiredFields = ['communityname', 'address', 'pincode', 'latitude', 'longitude'];
  for (const field of requiredFields) {
    if (!row[field] || row[field].trim() === '') {
      logger.warning(`Missing required field: ${field} in row: ${JSON.stringify(row)}`);
      return false;
    }
  }

  // Validate latitude and longitude are valid numbers
  const lat = parseFloat(row.latitude);
  const lng = parseFloat(row.longitude);
  if (isNaN(lat) || isNaN(lng)) {
    logger.warning(`Invalid coordinates in row: ${JSON.stringify(row)}`);
    return false;
  }

  return true;
}

/**
 * Transforms a CSV row into a Firestore document
 * @param {Object} row - The CSV row as an object
 * @returns {Object} - The formatted document for Firestore
 */
function transformRowToDocument(row) {
  // Validate the row first
  if (!validateRow(row)) {
    throw new Error(`Invalid row data: ${JSON.stringify(row)}`);
  }

  // Convert latitude and longitude to numbers
  const latitude = parseFloat(row.latitude);
  const longitude = parseFloat(row.longitude);

  // Convert pincode to number if possible
  let pincode = parseInt(row.pincode);
  if (isNaN(pincode)) {
    pincode = row.pincode; // Keep as string if not a valid number
  }

  // Create the document object
  return {
    communityName: row.communityname,
    address: row.address,
    pincode: pincode,
    location: {
      latitude: latitude,
      longitude: longitude
    },
    // Add GeoPoint for Firestore geoqueries (if needed later)
    geoPoint: {
      latitude: latitude,
      longitude: longitude
    },
    // Add timestamp for when the record was imported
    createdAt: new Date(),
    updatedAt: new Date(),
    // Add a searchable lowercase version of the community name for case-insensitive queries
    communityNameLower: row.communityname.toLowerCase(),
    // Flag to indicate this is an imported record
    imported: true
  };
}

/**
 * Imports data in batches to Firestore
 * @param {Array} records - Array of records to import
 * @returns {Promise<boolean>} - True if batch was imported successfully, false otherwise
 */
async function importBatch(records) {
  try {
    // Create a batch
    const batch = writeBatch(db);
    const collectionRef = collection(db, 'hyderabadProperties');

    // Add each record to the batch
    records.forEach(record => {
      const docRef = doc(collectionRef); // Auto-generate ID
      batch.set(docRef, record);
    });

    // Commit the batch
    await batch.commit();
    importedCount += records.length;
    logger.success(`Batch of ${records.length} records imported successfully. Total: ${importedCount}`);
    return true;
  } catch (error) {
    errorCount += records.length;
    logger.error(`Error importing batch of ${records.length} records:`, error);
    return false;
  }
}

/**
 * Main function to read CSV and import to Firestore
 */
async function importCSVToFirestore() {
  logger.info('Starting import process...');

  // Check if the CSV file exists
  if (!fs.existsSync(csvFilePath)) {
    logger.error(`CSV file not found: ${csvFilePath}`);
    return;
  }

  logger.info(`Reading CSV file: ${csvFilePath}`);

  // Create a readable stream for the CSV file
  const fileStream = fs.createReadStream(csvFilePath);

  // Configure the parser
  const parser = parse({
    columns: true, // Use the first line as column names
    skip_empty_lines: true,
    trim: true
  });

  // Get batch size from command line argument or use default
  const BATCH_SIZE = batchSizeArg
    ? parseInt(batchSizeArg.split('=')[1])
    : DEFAULT_BATCH_SIZE;

  logger.info(`Using batch size: ${BATCH_SIZE}`);

  // Array to hold records for batch processing
  let recordBatch = [];
  let rowCount = 0;

  // Process the CSV file
  fileStream
    .pipe(parser)
    .on('data', (row) => {
      rowCount++;
      try {
        // Transform the row into a Firestore document
        const document = transformRowToDocument(row);
        recordBatch.push(document);

        // When batch size is reached, import the batch
        if (recordBatch.length >= BATCH_SIZE) {
          importBatch([...recordBatch]); // Create a copy of the batch
          recordBatch = []; // Clear the batch
        }
      } catch (error) {
        errorCount++;
        logger.error(`Error processing row ${rowCount}: ${JSON.stringify(row)}`, error);
      }
    })
    .on('end', async () => {
      // Import any remaining records
      if (recordBatch.length > 0) {
        await importBatch(recordBatch);
      }

      logger.info('\n===== Import Summary =====');
      logger.success(`Total records processed: ${rowCount}`);
      logger.success(`Total records imported: ${importedCount}`);

      if (errorCount > 0) {
        logger.warning(`Total records with errors: ${errorCount}`);
      } else {
        logger.success('No errors encountered during import');
      }

      logger.info('Import process completed!');

      // Exit the process
      process.exit(0);
    })
    .on('error', (error) => {
      logger.error('Error parsing CSV:', error);
      process.exit(1);
    });
}

// Run the import function
importCSVToFirestore().catch(error => {
  logger.error('Unhandled error during import:', error);
  process.exit(1);
});
