import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Shield, RefreshCw, AlertCircle, CheckCircle, User } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import { useAuth } from '@/lib/AuthContext';
import { getUserDocument, setUserAsAdmin } from '@/lib/userService';
import { toast } from 'sonner';
import { UserRole } from '@/types';

const AdminDiagnostic: React.FC = () => {
  const { currentUser, isAdmin, checkAdminStatus, refreshUserData } = useAuth();
  const [loading, setLoading] = useState(false);
  const [diagnosing, setDiagnosing] = useState(false);
  const [userDoc, setUserDoc] = useState<any>(null);
  const [adminStatus, setAdminStatus] = useState<boolean | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [fixAttempted, setFixAttempted] = useState(false);

  // Function to capture console logs
  useEffect(() => {
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    
    console.log = (...args) => {
      originalConsoleLog(...args);
      setLogs(prev => [...prev, `LOG: ${args.map(arg => String(arg)).join(' ')}`]);
    };
    
    console.error = (...args) => {
      originalConsoleError(...args);
      setLogs(prev => [...prev, `ERROR: ${args.map(arg => String(arg)).join(' ')}`]);
    };
    
    return () => {
      console.log = originalConsoleLog;
      console.error = originalConsoleError;
    };
  }, []);

  const runDiagnostics = async () => {
    if (!currentUser) {
      toast.error('You must be signed in to run diagnostics');
      return;
    }

    try {
      setDiagnosing(true);
      setLogs([]);
      
      // Log basic user info
      console.log('Diagnostic: Current user:', currentUser.uid);
      console.log('Diagnostic: User email:', currentUser.email);
      console.log('Diagnostic: Email verified:', currentUser.emailVerified);
      
      // Check admin status from context
      console.log('Diagnostic: Admin status from context:', isAdmin);
      
      // Get user document from Firestore
      console.log('Diagnostic: Fetching user document from Firestore...');
      const doc = await getUserDocument(currentUser.uid);
      setUserDoc(doc);
      console.log('Diagnostic: User document:', doc);
      
      // Check admin status from server
      console.log('Diagnostic: Checking admin status from server...');
      const adminCheck = await checkAdminStatus();
      setAdminStatus(adminCheck);
      console.log('Diagnostic: Admin status from server:', adminCheck);
      
    } catch (error) {
      console.error('Diagnostic: Error running diagnostics:', error);
    } finally {
      setDiagnosing(false);
    }
  };

  const fixAdminAccess = async () => {
    if (!currentUser) {
      toast.error('You must be signed in to fix admin access');
      return;
    }

    try {
      setLoading(true);
      setLogs([]);
      
      console.log('Fix: Attempting to fix admin access for:', currentUser.email);
      
      // Direct database update to set user as admin
      await setUserAsAdmin(currentUser.uid);
      console.log('Fix: User set as admin in database');
      
      // Refresh user data
      await refreshUserData();
      console.log('Fix: User data refreshed');
      
      // Check admin status again
      const adminCheck = await checkAdminStatus();
      setAdminStatus(adminCheck);
      console.log('Fix: Admin status after fix:', adminCheck);
      
      // Get updated user document
      const doc = await getUserDocument(currentUser.uid);
      setUserDoc(doc);
      console.log('Fix: Updated user document:', doc);
      
      setFixAttempted(true);
      toast.success('Admin access fix attempted');
    } catch (error) {
      console.error('Fix: Error fixing admin access:', error);
      toast.error('Failed to fix admin access');
    } finally {
      setLoading(false);
    }
  };

  const isTargetAdmin = currentUser?.email === '<EMAIL>';
  const hasAdminRole = userDoc?.role === UserRole.Admin;

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow flex items-center justify-center py-8">
        <div className="max-w-3xl w-full mx-auto p-8 bg-white rounded-lg shadow-lg">
          <div className="text-center mb-6">
            <Shield className="h-16 w-16 text-burgundy-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-navy-800 mb-2">Admin Access Diagnostic</h1>
            <p className="text-gray-600">
              Troubleshooting admin access for {currentUser?.email || 'Not signed in'}
            </p>
          </div>
          
          {!currentUser ? (
            <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-6">
              <p className="font-semibold flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                You must be signed in to run diagnostics
              </p>
            </div>
          ) : (
            <>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                <h2 className="text-lg font-semibold mb-2">User Information:</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <div className="flex items-center">
                    <span className="font-medium mr-2">User ID:</span>
                    <span className="text-gray-700">{currentUser.uid}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">Email:</span>
                    <span className="text-gray-700">{currentUser.email}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">Email Verified:</span>
                    <span className={`${currentUser.emailVerified ? 'text-green-600' : 'text-red-600'}`}>
                      {currentUser.emailVerified ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">Admin Status:</span>
                    <span className={`${isAdmin ? 'text-green-600' : 'text-red-600'}`}>
                      {isAdmin ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
              </div>
              
              {userDoc && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                  <h2 className="text-lg font-semibold mb-2">Firestore Document:</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div className="flex items-center">
                      <span className="font-medium mr-2">Role:</span>
                      <span className={`${hasAdminRole ? 'text-green-600' : 'text-red-600'}`}>
                        {userDoc.role || 'Not set'}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium mr-2">Display Name:</span>
                      <span className="text-gray-700">{userDoc.displayName || 'Not set'}</span>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="flex flex-col md:flex-row justify-center gap-4 mb-6">
                <Button
                  onClick={runDiagnostics}
                  disabled={diagnosing}
                  className="flex items-center"
                >
                  {diagnosing ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Running Diagnostics...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Run Diagnostics
                    </>
                  )}
                </Button>
                
                <Button
                  onClick={fixAdminAccess}
                  disabled={loading || !isTargetAdmin}
                  variant={isTargetAdmin ? "default" : "outline"}
                  className="flex items-center"
                >
                  {loading ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Fixing Access...
                    </>
                  ) : (
                    <>
                      <Shield className="h-4 w-4 mr-2" />
                      Fix Admin Access
                    </>
                  )}
                </Button>
              </div>
              
              {!isTargetAdmin && (
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-6">
                  <p className="font-semibold flex items-center">
                    <AlertCircle className="h-5 w-5 mr-2" />
                    Admin fix is only <NAME_EMAIL>
                  </p>
                </div>
              )}
              
              {fixAttempted && adminStatus && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6 flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2" />
                  <p>Admin access fixed successfully! Try accessing the admin dashboard now.</p>
                </div>
              )}
            </>
          )}
          
          <div className="mt-8">
            <h2 className="text-lg font-semibold mb-2">Diagnostic Logs:</h2>
            <div className="bg-gray-50 border border-gray-200 rounded p-4 max-h-60 overflow-y-auto text-sm font-mono">
              {logs.length > 0 ? (
                logs.map((log, index) => (
                  <div 
                    key={index} 
                    className={`py-1 ${log.startsWith('ERROR') ? 'text-red-600' : 'text-gray-700'}`}
                  >
                    {log}
                  </div>
                ))
              ) : (
                <p className="text-gray-500 italic">No logs available yet. Run diagnostics to see logs.</p>
              )}
            </div>
          </div>
          
          <div className="flex justify-center mt-8">
            <Link to="/">
              <Button variant="outline" className="mr-4">
                Return to Home
              </Button>
            </Link>
            <Link to="/admin">
              <Button>
                Try Admin Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default AdminDiagnostic;
