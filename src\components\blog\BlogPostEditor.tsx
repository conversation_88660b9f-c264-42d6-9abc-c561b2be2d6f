import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import { BlogPost } from '@/lib/blogService';
import { uploadCoverImage } from '@/lib/blogService';
import { createBlogPost, updateBlogPost } from '@/lib/blogService';
import BlogStatusBadge from './BlogStatusBadge';

interface BlogPostEditorProps {
  initialPost?: BlogPost | null;
  onSave?: (postId: string) => void;
  onCancel?: () => void;
}

const BlogPostEditor: React.FC<BlogPostEditorProps> = ({ initialPost = null, onSave, onCancel }) => {
  const [title, setTitle] = useState(initialPost?.title || '');
  const [content, setContent] = useState(initialPost?.content || '');
  const [tags, setTags] = useState<string[]>(initialPost?.tags || []);
  const [author, setAuthor] = useState(initialPost?.author || '');
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [coverImageUrl, setCoverImageUrl] = useState(initialPost?.coverImageUrl || '');
  const [draft, setDraft] = useState(initialPost?.draft || true);
  const [published, setPublished] = useState(initialPost?.published || false);
  const [slug, setSlug] = useState(initialPost?.slug || '');
  const [isSaving, setIsSaving] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Auto-generate slug when title changes
  useEffect(() => {
    if (title && !slug) {
      setSlug(generateSlug(title));
    }
  }, [title]);

  const handleTagInputChange = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && e.currentTarget.value.trim() !== '') {
      e.preventDefault();
      const newTag = e.currentTarget.value.trim().toLowerCase();
      if (!tags.includes(newTag)) {
        setTags([...tags, newTag]);
      }
      e.currentTarget.value = '';
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSave = async () => {
    // Validate required fields
    const errors: string[] = [];
    if (!title.trim()) errors.push('Title is required');
    if (!content.trim()) errors.push('Content is required');
    if (!author.trim()) errors.push('Author is required');
    
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    setIsSaving(true);
    
    try {
      let postId: string;
      let imageUrl = coverImageUrl;
      
      // Upload cover image if selected
      if (coverImage) {
        imageUrl = await uploadCoverImage(coverImage);
      }
      
      // Create or update the blog post
      if (initialPost && initialPost.id) {
        await updateBlogPost(initialPost.id, {
          title,
          content,
          tags,
          author,
          coverImageUrl: imageUrl,
          draft: !published,
          published
        });
        postId = initialPost.id;
      } else {
        postId = await createBlogPost({
          title,
          content,
          tags,
          author,
          coverImageUrl: imageUrl,
          published,
          draft: !published,
          slug
        });
      }
      
      // Reset validation errors
      setValidationErrors([]);
      
      // Notify parent component if provided
      if (onSave) {
        onSave(postId);
      }
    } catch (error) {
      console.error('Error saving blog post:', error);
      setValidationErrors(['Error saving blog post. Please try again.']);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAutoSave = async () => {
    if (initialPost && initialPost.id) {
      await autoSaveDraft(initialPost.id, {
        title,
        content,
        tags,
        author,
        coverImageUrl: coverImageUrl,
        draft: true
      });
    }
  };

  const handlePublishToggle = async () => {
    if (initialPost && initialPost.id) {
      await togglePublishStatus(initialPost.id, !published);
      setPublished(!published);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        {initialPost ? 'Edit Blog Post' : 'Create New Blog Post'}
      </h2>
      
      {validationErrors.length > 0 && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded">
          <ul className="list-disc pl-5">
            {validationErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Title
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter blog post title"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Slug
          </label>
          <input
            type="text"
            value={slug}
            onChange={(e) => setSlug(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter URL-friendly slug"
          />
          <p className="mt-1 text-sm text-gray-500">
            Auto-generated from title if left blank
          </p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Author
          </label>
          <input
            type="text"
            value={author}
            onChange={(e) => setAuthor(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter author name"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Tags
          </label>
          <div className="flex flex-wrap items-center px-3 py-2 border border-gray-300 rounded-md">
            <div className="flex flex-wrap gap-2 mb-2">
              {tags.map((tag) => (
                <span 
                  key={tag} 
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2 mb-2"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="ml-1 text-blue-600 hover:text-blue-800 focus:outline-none"
                  >
                    <span className="sr-only">Remove tag</span>
                    <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 10.586l3.293-3.293a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.414 10l1.414-1.414a1 1 0 1 0-1.414-1.414L10 8.586 8.586 7.172 1 1 0 1.414 1.414L11.41
