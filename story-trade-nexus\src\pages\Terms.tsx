import React from 'react';
import MainLayout from '@/components/layouts/MainLayout';

const Terms = () => {
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12 max-w-4xl">
        <div className="bg-white rounded-lg shadow-md p-8">
          <h1 className="text-3xl font-bold text-navy-800 font-playfair mb-6">Terms of Service</h1>
          <p className="text-gray-600 mb-4">Effective Date: 23-05-2025</p>

          <div className="prose prose-burgundy max-w-none">
            <p className="mb-6">
              Welcome to PeerBooks. These Terms of Service ("Terms") govern your use of our website, mobile application, and services (collectively, the "Service"). By using PeerBooks, you agree to these Terms. If you do not agree to these Terms, please do not use the Service.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">1. Acceptance of Terms</h2>
            <p>
              By accessing or using the Service, you agree to be bound by these Terms. If you are using the Service on behalf of an organization, you are agreeing to these Terms for that organization and promising that you have the authority to bind that organization to these Terms.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">2. Description of Service</h2>
            <p>
              PeerBooks is a peer-to-peer platform that allows users to rent, buy, and exchange used books directly with each other. We provide the platform to connect users but are not directly involved in transactions between users.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">3. User Accounts</h2>
            <p>
              To use certain features of the Service, you must register for an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.
            </p>
            <p>
              You are responsible for safeguarding your password and for all activities that occur under your account. You agree to notify us immediately of any unauthorized use of your account.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">4. User Content</h2>
            <p>
              You retain ownership of any content you submit to the Service, including book listings, reviews, and comments ("User Content"). By submitting User Content, you grant us a worldwide, non-exclusive, royalty-free license to use, copy, modify, and display your User Content in connection with the Service.
            </p>
            <p>
              You are solely responsible for your User Content and the consequences of posting it. You represent and warrant that you own or have the necessary rights to post your User Content, and that your User Content does not violate the rights of any third party.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">5. Prohibited Conduct</h2>
            <p>
              You agree not to:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>Use the Service for any illegal purpose or in violation of any laws</li>
              <li>Post false, misleading, or deceptive content</li>
              <li>Impersonate any person or entity</li>
              <li>Harass, abuse, or harm another person</li>
              <li>Interfere with or disrupt the Service</li>
              <li>Attempt to gain unauthorized access to the Service</li>
              <li>Use the Service to send spam or unsolicited messages</li>
            </ul>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">6. Transactions Between Users</h2>
            <p>
              PeerBooks is not responsible for transactions between users. We do not guarantee the quality, safety, or legality of items listed, the truth or accuracy of listings, or the ability of users to complete transactions.
            </p>
            <p>
              Users are solely responsible for all aspects of their interactions and transactions with other users.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">7. Termination</h2>
            <p>
              We may terminate or suspend your account and access to the Service at our sole discretion, without notice, for conduct that we believe violates these Terms or is harmful to other users, us, or third parties, or for any other reason.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">8. Disclaimer of Warranties</h2>
            <p>
              THE SERVICE IS PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">9. Limitation of Liability</h2>
            <p>
              IN NO EVENT SHALL PEERBOOKS BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE SERVICE.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">10. Changes to Terms</h2>
            <p>
              We may modify these Terms at any time. If we make changes, we will provide notice of such changes, such as by sending an email, providing a notice through the Service, or updating the date at the top of these Terms. Your continued use of the Service following the posting of revised Terms means that you accept and agree to the changes.
            </p>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">11. Contact Information</h2>
            <p>
              If you have any questions about these Terms, please contact us at: <a href="mailto:<EMAIL>" className="text-burgundy-500 hover:underline"><EMAIL></a>
            </p>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Terms;
