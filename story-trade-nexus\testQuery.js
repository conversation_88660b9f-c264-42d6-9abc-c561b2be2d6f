#!/usr/bin/env node

/**
 * Test script for the Firebase Community Query Tool
 * 
 * This script tests the queryCommunities.js functionality with various inputs
 * to ensure it works correctly.
 * 
 * Usage: node testQuery.js
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { spawn } from 'child_process';
import process from 'process';

// Test cases with expected behaviors
const testCases = [
  {
    name: 'Valid Pincode Test',
    input: '500001',
    expectSuccess: true,
    description: 'Should return communities for a valid pincode'
  },
  {
    name: 'Another Valid Pincode Test',
    input: '560001',
    expectSuccess: true,
    description: 'Should return communities for another valid pincode'
  },
  {
    name: 'Invalid Pincode - Too Short',
    input: '50001',
    expectSuccess: false,
    description: 'Should reject pincode with less than 6 digits'
  },
  {
    name: 'Invalid Pincode - Too Long',
    input: '5000011',
    expectSuccess: false,
    description: 'Should reject pincode with more than 6 digits'
  },
  {
    name: 'Invalid Pincode - Contains Letters',
    input: 'abc123',
    expectSuccess: false,
    description: 'Should reject pincode with letters'
  },
  {
    name: 'Invalid Pincode - Contains Spaces',
    input: '500 001',
    expectSuccess: false,
    description: 'Should reject pincode with spaces'
  },
  {
    name: 'Help Flag Test',
    input: '--help',
    expectSuccess: true,
    description: 'Should display help information'
  },
  {
    name: 'Version Flag Test',
    input: '--version',
    expectSuccess: true,
    description: 'Should display version information'
  }
];

/**
 * Runs a single test case
 * @param {Object} testCase - The test case to run
 * @returns {Promise<Object>} - Test result
 */
function runTest(testCase) {
  return new Promise((resolve) => {
    console.log(`\n🧪 Running: ${testCase.name}`);
    console.log(`📝 Description: ${testCase.description}`);
    console.log(`📥 Input: "${testCase.input}"`);
    
    const child = spawn('node', ['queryCommunities.js', testCase.input], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      const success = testCase.expectSuccess ? code === 0 : code !== 0;
      
      console.log(`📤 Exit Code: ${code}`);
      console.log(`✅ Expected Success: ${testCase.expectSuccess}`);
      console.log(`🎯 Test Result: ${success ? 'PASS' : 'FAIL'}`);
      
      if (stdout) {
        console.log(`📄 Output Preview: ${stdout.substring(0, 100)}...`);
      }
      
      if (stderr && !testCase.expectSuccess) {
        console.log(`⚠️  Error Output: ${stderr.substring(0, 100)}...`);
      }
      
      resolve({
        name: testCase.name,
        input: testCase.input,
        success: success,
        exitCode: code,
        stdout: stdout,
        stderr: stderr
      });
    });
    
    child.on('error', (error) => {
      console.log(`❌ Process Error: ${error.message}`);
      resolve({
        name: testCase.name,
        input: testCase.input,
        success: false,
        exitCode: -1,
        error: error.message
      });
    });
  });
}

/**
 * Runs all test cases
 */
async function runAllTests() {
  console.log('🚀 Firebase Community Query Tool - Test Suite');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const testCase of testCases) {
    const result = await runTest(testCase);
    results.push(result);
    
    // Add a small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Display summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));
  
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / results.length) * 100).toFixed(1)}%`);
  
  if (failed > 0) {
    console.log('\n❌ Failed Tests:');
    results.filter(r => !r.success).forEach(result => {
      console.log(`  • ${result.name} (Input: "${result.input}")`);
    });
  }
  
  console.log('\n🏁 Test suite completed!');
  
  // Exit with appropriate code
  process.exit(failed > 0 ? 1 : 0);
}

// Run the test suite
runAllTests().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
