/**
 * LazyIcon Component
 * 
 * Lazy-loaded wrapper for lucide-react icons to reduce initial bundle size
 * Only loads icons when they are actually needed
 */

import React, { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Icon loading skeleton
const IconSkeleton: React.FC<{ size?: number; className?: string }> = ({ 
  size = 24, 
  className = '' 
}) => (
  <Skeleton 
    className={`inline-block ${className}`} 
    style={{ width: `${size}px`, height: `${size}px` }}
  />
);

// Common icon props interface
interface IconProps {
  size?: number;
  className?: string;
  color?: string;
  strokeWidth?: number;
  fallback?: React.ReactNode;
}

// Lazy load commonly used icons
const BookIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Book }))
);

const UserIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.User }))
);

const SearchIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Search }))
);

const PlusIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Plus }))
);

const EditIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Edit }))
);

const TrashIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Trash2 }))
);

const EyeIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Eye }))
);

const EyeOffIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.EyeOff }))
);

const HeartIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Heart }))
);

const ShareIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Share2 }))
);

const ArrowLeftIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.ArrowLeft }))
);

const ArrowRightIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.ArrowRight }))
);

const ChevronLeftIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.ChevronLeft }))
);

const ChevronRightIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.ChevronRight }))
);

const SettingsIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Settings }))
);

const MenuIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Menu }))
);

const XIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.X }))
);

const CheckIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Check }))
);

const AlertCircleIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.AlertCircle }))
);

const InfoIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Info }))
);

const LoaderIcon = React.lazy(() => 
  import('lucide-react').then(module => ({ default: module.Loader2 }))
);

// Lazy icon wrapper components
export const LazyBookIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <BookIcon {...props} />
  </Suspense>
);

export const LazyUserIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <UserIcon {...props} />
  </Suspense>
);

export const LazySearchIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <SearchIcon {...props} />
  </Suspense>
);

export const LazyPlusIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <PlusIcon {...props} />
  </Suspense>
);

export const LazyEditIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <EditIcon {...props} />
  </Suspense>
);

export const LazyTrashIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <TrashIcon {...props} />
  </Suspense>
);

export const LazyEyeIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <EyeIcon {...props} />
  </Suspense>
);

export const LazyEyeOffIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <EyeOffIcon {...props} />
  </Suspense>
);

export const LazyHeartIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <HeartIcon {...props} />
  </Suspense>
);

export const LazyShareIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <ShareIcon {...props} />
  </Suspense>
);

export const LazyArrowLeftIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <ArrowLeftIcon {...props} />
  </Suspense>
);

export const LazyArrowRightIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <ArrowRightIcon {...props} />
  </Suspense>
);

export const LazyChevronLeftIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <ChevronLeftIcon {...props} />
  </Suspense>
);

export const LazyChevronRightIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <ChevronRightIcon {...props} />
  </Suspense>
);

export const LazySettingsIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <SettingsIcon {...props} />
  </Suspense>
);

export const LazyMenuIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <MenuIcon {...props} />
  </Suspense>
);

export const LazyXIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <XIcon {...props} />
  </Suspense>
);

export const LazyCheckIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <CheckIcon {...props} />
  </Suspense>
);

export const LazyAlertCircleIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <AlertCircleIcon {...props} />
  </Suspense>
);

export const LazyInfoIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <InfoIcon {...props} />
  </Suspense>
);

export const LazyLoaderIcon: React.FC<IconProps> = ({ fallback, ...props }) => (
  <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
    <LoaderIcon {...props} />
  </Suspense>
);

// Generic lazy icon loader for any lucide icon
export const LazyIcon: React.FC<IconProps & { name: string }> = ({ 
  name, 
  fallback, 
  ...props 
}) => {
  const IconComponent = React.lazy(() => 
    import('lucide-react').then(module => ({ default: module[name] }))
  );

  return (
    <Suspense fallback={fallback || <IconSkeleton size={props.size} className={props.className} />}>
      <IconComponent {...props} />
    </Suspense>
  );
};

// Export skeleton for external use
export { IconSkeleton };

// Default export for convenience
export default {
  Book: LazyBookIcon,
  User: LazyUserIcon,
  Search: LazySearchIcon,
  Plus: LazyPlusIcon,
  Edit: LazyEditIcon,
  Trash: LazyTrashIcon,
  Eye: LazyEyeIcon,
  EyeOff: LazyEyeOffIcon,
  Heart: LazyHeartIcon,
  Share: LazyShareIcon,
  ArrowLeft: LazyArrowLeftIcon,
  ArrowRight: LazyArrowRightIcon,
  ChevronLeft: LazyChevronLeftIcon,
  ChevronRight: LazyChevronRightIcon,
  Settings: LazySettingsIcon,
  Menu: LazyMenuIcon,
  X: LazyXIcon,
  Check: LazyCheckIcon,
  AlertCircle: LazyAlertCircleIcon,
  Info: LazyInfoIcon,
  Loader: LazyLoaderIcon,
  Generic: LazyIcon,
  Skeleton: IconSkeleton
};
