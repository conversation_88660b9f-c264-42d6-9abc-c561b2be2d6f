# Properties Update Solution

This document provides a complete solution for updating the Firebase Firestore collection 'hyderabadProperties' with new property data from configurable CSV files.

## 📁 Files Created

### 1. Main Update Script
- **File**: `src/scripts/updatePropertiesFromCSV.ts`
- **Purpose**: Main TypeScript script to update Firestore with CSV data
- **Features**: Configurable CSV input, data validation, batch processing, duplicate prevention, dry-run mode

### 2. CSV Validation Script
- **File**: `src/scripts/validatePropertiesCSV.ts`
- **Purpose**: Validates CSV file format and data integrity before running the main script
- **Features**: Configurable CSV input, comprehensive validation, statistics reporting, error detection

### 3. Documentation
- **File**: `src/scripts/README-updateHyderabadProperties.md`
- **Purpose**: Detailed documentation for using the update script
- **Content**: Usage instructions, troubleshooting, examples

### 4. Package.json Updates
- **Added**: `tsx` as dev dependency for TypeScript execution
- **Added**: New npm scripts for easy execution

## 🚀 Quick Start

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Validate CSV File
```bash
# Default CSV file
npm run validate:csv

# Custom CSV file
npm run validate:csv -- --csv-file=my-data.csv
```

### Step 3: Dry Run (Preview Changes)
```bash
# Default CSV file
npm run update:properties:dry

# Custom CSV file
npm run update:properties:dry -- --csv-file=my-data.csv
```

### Step 4: Live Update (Actual Database Changes)
```bash
# Default CSV file
npm run update:properties

# Custom CSV file
npm run update:properties -- --csv-file=my-data.csv
```

## 📋 Available NPM Scripts

| Script | Command | Description |
|--------|---------|-------------|
| `validate:csv` | `npx tsx src/scripts/validatePropertiesCSV.ts` | Validate CSV file format |
| `update:properties:dry` | `npx tsx src/scripts/updatePropertiesFromCSV.ts --dry-run` | Preview changes |
| `update:properties` | `npx tsx src/scripts/updatePropertiesFromCSV.ts` | Update database |

## 🔧 Script Features

### Data Validation
- ✅ **Configurable CSV Input**: Support for custom CSV file paths via command-line arguments
- ✅ **Required Field Validation**: Validates communityname, address, pincode, latitude, longitude
- ✅ **Pincode Format Validation**: Accepts any valid 6-digit Indian postal codes (no range restrictions)
- ✅ **Coordinate Range Validation**: Latitude (-90 to 90), longitude (-180 to 180)
- ✅ **Data Type Validation**: Ensures proper data types and sanitization
- ✅ **Length Limits**: Community name (200 chars), address (500 chars)

### Conflict Handling
- ✅ **Merge Strategy**: Add new records, skip existing ones
- ✅ **Duplicate Detection**: Based on `communityNameLower` + `pincode` combination
- ✅ **Data Preservation**: Existing records are never overwritten

### Performance & Reliability
- ✅ **Batch Processing**: Processes data in configurable batches (default: 500)
- ✅ **Rate Limiting**: 1-second delays between batches to prevent API throttling
- ✅ **Error Isolation**: Individual record errors don't stop the entire process
- ✅ **Memory Efficiency**: Streams CSV data to minimize memory usage

### Safety Features
- ✅ **Dry Run Mode**: Preview changes without updating the database
- ✅ **Comprehensive Logging**: Detailed progress reporting with timestamps
- ✅ **Error Handling**: Graceful error handling with detailed error messages
- ✅ **Validation First**: All data validated before any database operations

## 📊 Document Structure

Each document in the `hyderabadProperties` collection will have this structure:

```typescript
{
  communityName: string;           // Original community name from CSV
  address: string;                 // Full address from CSV
  pincode: number | string;        // Postal code (converted to number if valid)
  location: {                      // Coordinate object for queries
    latitude: number;
    longitude: number;
  };
  geoPoint: {                      // GeoPoint for Firestore geoqueries
    latitude: number;
    longitude: number;
  };
  createdAt: Date;                 // Document creation timestamp
  updatedAt: Date;                 // Last update timestamp
  communityNameLower: string;      // Lowercase name for case-insensitive queries
  imported: boolean;               // Flag indicating this is an imported record
  source: string;                  // Source CSV filename for tracking
  batchId: string;                 // Unique batch identifier for this import
}
```

## 🔍 CSV File Requirements

### File Location
- **Default**: `hyderabad_properties.csv` in project root directory
- **Custom**: Any path specified with `--csv-file=<path>` option

### Required Columns
| Column | Type | Description | Validation |
|--------|------|-------------|------------|
| `communityname` | string | Property/community name | Required, max 200 chars |
| `address` | string | Full address | Required, max 500 chars |
| `pincode` | string | 6-digit postal code | Required, must be 6 digits |
| `latitude` | string | Latitude coordinate | Required, -90 to 90 |
| `longitude` | string | Longitude coordinate | Required, -180 to 180 |

### Example CSV Format
```csv
communityname,address,pincode,latitude,longitude
"Nestcon's Chintala Residency - Alwal","Survey No.16, 1-6-46 & 47, Panta Reddy Colony, Tirumala Enclave, Alwal, Secunderabad, Telangana 500010, India",500010,17.5017236,78.4992728
```

## 🛠️ Command Line Options

### Update Script Options
```bash
# Basic dry run with default CSV
npm run update:properties:dry

# Live update with default CSV
npm run update:properties

# Custom CSV file with dry run
npm run update:properties:dry -- --csv-file=my-data.csv

# Custom CSV with batch size
npm run update:properties -- --csv-file=my-data.csv --batch-size=250

# Help information
npm run update:properties -- --help
```

## 📈 Expected Output

### Validation Script Output
```
ℹ️  Starting CSV validation...
✅ Read 1708 records from CSV

📊 CSV Validation Results
========================
ℹ️  Total rows: 1708
✅ Valid rows: 1705
❌ Invalid rows: 3

📍 Pincode Analysis
------------------
ℹ️  Unique pincodes: 31
ℹ️  Pincode range: 500010 - 500040
✅ Pincode range matches expected values

🗺️  Coordinate Analysis
----------------------
ℹ️  Latitude range: 17.200000 to 17.700000
ℹ️  Longitude range: 78.200000 to 78.700000
✅ Coordinates appear to be in Hyderabad area

🎯 Recommendation
----------------
✅ CSV file is valid and ready for import!
```

### Update Script Output
```
[INFO] Starting Hyderabad properties update process...
[INFO] 🔍 Running in DRY RUN mode - no actual database changes will be made
[SUCCESS] Firebase initialized successfully
[SUCCESS] Successfully read 1708 records from CSV
[INFO] Validation complete: 1705 valid, 3 invalid records
[INFO] Processing 1705 documents in batches of 500...

===== Update Summary =====
[SUCCESS] Total CSV records read: 1708
[SUCCESS] Valid records: 1705
[SUCCESS] Records skipped (validation errors): 3
[INFO] [DRY RUN] Would add 1650 new records to Firestore
[SUCCESS] No errors encountered during processing
[SUCCESS] Processing completed in 8.5 seconds
```

## 🔗 Integration with Existing Code

This solution integrates seamlessly with your existing codebase:

- **Firebase Configuration**: Uses existing `src/lib/firebase.ts` configuration
- **Document Structure**: Matches the structure from `importHyderabadProperties.js`
- **Community Utils**: Compatible with `src/lib/communityUtils.ts`
- **Pincode Services**: Works with existing pincode location services
- **TypeScript**: Full TypeScript support with proper interfaces

## 🚨 Safety Recommendations

1. **Always run validation first**: `npm run validate:csv`
2. **Always dry run first**: `npm run update:properties:dry`
3. **Check Firebase connection**: Ensure proper Firebase configuration
4. **Backup data**: Consider backing up existing data before large imports
5. **Monitor logs**: Watch for any error messages during execution
6. **Test with small batches**: Use `--batch-size=50` for initial testing

## 🆘 Troubleshooting

### Common Issues

1. **CSV file not found**: Ensure file is in project root directory
2. **Firebase connection error**: Check `src/lib/firebase.ts` configuration
3. **TypeScript errors**: Run `npm install` to ensure all dependencies are installed
4. **Permission errors**: Ensure Firebase project has proper permissions
5. **Memory issues**: Use smaller batch sizes for large CSV files

### Getting Help

1. Check the detailed README: `src/scripts/README-updateHyderabadProperties.md`
2. Review validation output for specific errors
3. Check Firebase console for any service issues
4. Verify CSV file format matches requirements

## ✅ Success Criteria

After successful execution, you should see:
- New property records added to the `hyderabadProperties` collection
- No duplicate entries (existing records preserved)
- All records properly formatted with required fields
- Batch tracking information for audit purposes
- Detailed logs showing the import process

The solution is production-ready and follows best practices for data import operations with Firebase Firestore.
