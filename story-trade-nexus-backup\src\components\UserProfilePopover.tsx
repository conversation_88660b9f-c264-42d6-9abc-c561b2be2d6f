import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  User,
  Mail,
  BookOpen,
  Heart,
  Calendar,
  ChevronRight,
  BookMarked,
  Clock,
  LayoutDashboard,
  Settings,
  LogOut,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/lib/AuthContext';
import { getBooksByOwner } from '@/lib/bookService';
import { Book, BookApprovalStatus } from '@/types';
import { toast } from 'sonner';

interface UserProfilePopoverProps {
  children: React.ReactNode;
}

const UserProfilePopover: React.FC<UserProfilePopoverProps> = ({ children }) => {
  const { currentUser, userData, signOut, emailVerified, sendVerificationEmail } = useAuth();
  const [userBooks, setUserBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [isSendingVerification, setIsSendingVerification] = useState(false);
  const navigate = useNavigate();

  // Handle direct navigation to dashboard on profile click
  const handleProfileClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setOpen(false);
    navigate('/dashboard');
  };

  // Handle sending verification email
  const handleSendVerification = async () => {
    if (!currentUser) return;

    setIsSendingVerification(true);
    try {
      await sendVerificationEmail();
      toast.success("Verification email sent! Please check your inbox.");
    } catch (error) {
      console.error("Error sending verification email:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to send verification email";
      toast.error(errorMessage);
    } finally {
      setIsSendingVerification(false);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
      setOpen(false);
      navigate('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser || !open) return;

      try {
        setLoading(true);

        // Fetch user's books
        const books = await getBooksByOwner(currentUser.uid);
        setUserBooks(books);
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [currentUser, open]);

  // Format date to readable format
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';

    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);

    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  // Get user initials for avatar fallback
  const getInitials = () => {
    if (userData?.displayName) {
      return userData.displayName
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .substring(0, 2);
    }
    return currentUser?.email?.substring(0, 2).toUpperCase() || "U";
  };

  // Get book counts
  const booksAdded = userBooks.length || 0;
  const approvedBooks = userBooks.filter(book =>
    book.approvalStatus === BookApprovalStatus.Approved || !book.approvalStatus).length;
  const pendingCount = userBooks.filter(book =>
    book.approvalStatus === BookApprovalStatus.Pending).length;
  const wishlistCount = userData?.wishlist?.length || 0;

  if (!currentUser || !userData) {
    return <>{children}</>;
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div onClick={handleProfileClick}>
          {children}
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex flex-col">
          {/* Header with user info */}
          <div className="bg-gray-50 p-4 rounded-t-md">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage
                  src={userData.photoURL || ""}
                  alt={userData.displayName || "User"}
                />
                <AvatarFallback className="bg-burgundy-100 text-burgundy-700">
                  {getInitials()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-navy-800 truncate">
                  {userData.displayName}
                </h3>
                <p className="text-sm text-gray-500 truncate">{userData.email}</p>
                <div className="flex items-center mt-1">
                  {emailVerified ? (
                    <div className="flex items-center text-green-600 text-xs">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      <span>Email verified</span>
                    </div>
                  ) : (
                    <div className="flex items-center text-amber-600 text-xs">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      <span>Email not verified</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="mt-3 text-xs text-gray-500 flex items-center">
              <Calendar className="h-3 w-3 mr-1" />
              <span>Member since {formatDate(userData.createdAt)}</span>
            </div>
          </div>

          {/* User stats */}
          <div className="p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Your Book Stats</h4>
            {loading ? (
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-gray-50 p-2 rounded-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <BookOpen className="h-3.5 w-3.5 text-burgundy-500 mr-1.5" />
                      <span className="text-xs">Total Books</span>
                    </div>
                    <span className="font-medium text-sm">{booksAdded}</span>
                  </div>
                </div>
                <div className="bg-gray-50 p-2 rounded-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <BookMarked className="h-3.5 w-3.5 text-green-500 mr-1.5" />
                      <span className="text-xs">Active</span>
                    </div>
                    <span className="font-medium text-sm">{approvedBooks}</span>
                  </div>
                </div>
                <div className="bg-gray-50 p-2 rounded-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Clock className="h-3.5 w-3.5 text-amber-500 mr-1.5" />
                      <span className="text-xs">Pending</span>
                    </div>
                    <span className="font-medium text-sm">{pendingCount}</span>
                  </div>
                </div>
                <div className="bg-gray-50 p-2 rounded-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Heart className="h-3.5 w-3.5 text-red-500 mr-1.5" />
                      <span className="text-xs">Wishlist</span>
                    </div>
                    <span className="font-medium text-sm">{wishlistCount}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Quick links */}
          <div className="border-t border-gray-100 p-2">
            <Link
              to="/dashboard"
              className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md"
              onClick={() => setOpen(false)}
            >
              <div className="flex items-center">
                <LayoutDashboard className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm">Dashboard</span>
              </div>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </Link>
            <Link
              to="/profile"
              className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md"
              onClick={() => setOpen(false)}
            >
              <div className="flex items-center">
                <User className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm">Profile</span>
              </div>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </Link>
            <Link
              to="/my-books"
              className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md"
              onClick={() => setOpen(false)}
            >
              <div className="flex items-center">
                <BookOpen className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm">My Books</span>
              </div>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </Link>
            <Link
              to="/wishlist"
              className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md"
              onClick={() => setOpen(false)}
            >
              <div className="flex items-center">
                <Heart className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm">Wishlist</span>
              </div>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </Link>
            <Link
              to="/settings"
              className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md"
              onClick={() => setOpen(false)}
            >
              <div className="flex items-center">
                <Settings className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm">Settings</span>
              </div>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </Link>

            {!emailVerified && (
              <div className="border-t border-gray-100 mt-2 pt-2">
                <button
                  onClick={handleSendVerification}
                  disabled={isSendingVerification}
                  className="flex items-center justify-between w-full p-2 hover:bg-amber-50 rounded-md text-left text-amber-700"
                >
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-2" />
                    <span className="text-sm">{isSendingVerification ? "Sending..." : "Verify Email"}</span>
                  </div>
                  <ChevronRight className="h-4 w-4 text-amber-400" />
                </button>
                <Link
                  to="/verify-email"
                  className="flex items-center justify-between w-full p-2 hover:bg-gray-50 rounded-md text-left"
                  onClick={() => setOpen(false)}
                >
                  <div className="flex items-center">
                    <AlertTriangle className="h-4 w-4 text-amber-500 mr-2" />
                    <span className="text-sm">Go to Verification Page</span>
                  </div>
                  <ChevronRight className="h-4 w-4 text-gray-400" />
                </Link>
              </div>
            )}

            <div className="border-t border-gray-100 mt-2 pt-2">
              <button
                onClick={handleSignOut}
                className="flex items-center justify-between w-full p-2 hover:bg-gray-50 rounded-md text-left"
              >
                <div className="flex items-center">
                  <LogOut className="h-4 w-4 text-red-500 mr-2" />
                  <span className="text-sm">Sign Out</span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default UserProfilePopover;
