import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import { toast } from 'sonner';
import AdminLayout from '@/components/layout/AdminLayout';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const AdminSettings: React.FC = () => {
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  // Placeholder settings - in a real app, these would be loaded from and saved to the database
  const [settings, setSettings] = useState({
    general: {
      siteName: 'Book Sharing Platform',
      contactEmail: '<EMAIL>',
      enableRegistration: true,
      requireEmailVerification: true,
    },
    books: {
      requireApproval: true,
      maxBooksPerUser: 50,
      allowMultipleImages: true,
      defaultBookAvailability: 'Available',
    },
    notifications: {
      enableEmailNotifications: true,
      notifyOnNewUser: true,
      notifyOnBookSubmission: true,
      adminEmailRecipients: '<EMAIL>',
    }
  });

  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would save the settings to the database here
      
      toast.success('Settings saved successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (section: keyof typeof settings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  return (
    <AdminLayout title="Admin Settings" description="Configure admin preferences and system settings">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-navy-800 mb-2">Admin Settings</h1>
          <p className="text-gray-600">Configure system settings and preferences</p>
        </div>
        
        <Button 
          onClick={handleSaveSettings} 
          disabled={isSaving}
          className="mt-4 md:mt-0"
        >
          {isSaving ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Settings'
          )}
        </Button>
      </div>

      <Tabs defaultValue="general" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="books">Books</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Configure general platform settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="siteName">Site Name</Label>
                <Input 
                  id="siteName" 
                  value={settings.general.siteName} 
                  onChange={(e) => handleInputChange('general', 'siteName', e.target.value)}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="contactEmail">Contact Email</Label>
                <Input 
                  id="contactEmail" 
                  type="email" 
                  value={settings.general.contactEmail} 
                  onChange={(e) => handleInputChange('general', 'contactEmail', e.target.value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="enableRegistration">Enable User Registration</Label>
                  <p className="text-sm text-muted-foreground">Allow new users to register on the platform</p>
                </div>
                <Switch 
                  id="enableRegistration" 
                  checked={settings.general.enableRegistration}
                  onCheckedChange={(checked) => handleInputChange('general', 'enableRegistration', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="requireEmailVerification">Require Email Verification</Label>
                  <p className="text-sm text-muted-foreground">Users must verify their email before accessing the platform</p>
                </div>
                <Switch 
                  id="requireEmailVerification" 
                  checked={settings.general.requireEmailVerification}
                  onCheckedChange={(checked) => handleInputChange('general', 'requireEmailVerification', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="books">
          <Card>
            <CardHeader>
              <CardTitle>Book Settings</CardTitle>
              <CardDescription>Configure book-related settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="requireApproval">Require Book Approval</Label>
                  <p className="text-sm text-muted-foreground">New book submissions require admin approval</p>
                </div>
                <Switch 
                  id="requireApproval" 
                  checked={settings.books.requireApproval}
                  onCheckedChange={(checked) => handleInputChange('books', 'requireApproval', checked)}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="maxBooksPerUser">Maximum Books Per User</Label>
                <Input 
                  id="maxBooksPerUser" 
                  type="number" 
                  value={settings.books.maxBooksPerUser.toString()} 
                  onChange={(e) => handleInputChange('books', 'maxBooksPerUser', parseInt(e.target.value))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="allowMultipleImages">Allow Multiple Images</Label>
                  <p className="text-sm text-muted-foreground">Users can upload multiple images per book</p>
                </div>
                <Switch 
                  id="allowMultipleImages" 
                  checked={settings.books.allowMultipleImages}
                  onCheckedChange={(checked) => handleInputChange('books', 'allowMultipleImages', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>Configure email and system notifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="enableEmailNotifications">Enable Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">Send email notifications for important events</p>
                </div>
                <Switch 
                  id="enableEmailNotifications" 
                  checked={settings.notifications.enableEmailNotifications}
                  onCheckedChange={(checked) => handleInputChange('notifications', 'enableEmailNotifications', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifyOnNewUser">Notify on New User Registration</Label>
                  <p className="text-sm text-muted-foreground">Send notification when a new user registers</p>
                </div>
                <Switch 
                  id="notifyOnNewUser" 
                  checked={settings.notifications.notifyOnNewUser}
                  onCheckedChange={(checked) => handleInputChange('notifications', 'notifyOnNewUser', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifyOnBookSubmission">Notify on Book Submission</Label>
                  <p className="text-sm text-muted-foreground">Send notification when a new book is submitted</p>
                </div>
                <Switch 
                  id="notifyOnBookSubmission" 
                  checked={settings.notifications.notifyOnBookSubmission}
                  onCheckedChange={(checked) => handleInputChange('notifications', 'notifyOnBookSubmission', checked)}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="adminEmailRecipients">Admin Email Recipients</Label>
                <Input 
                  id="adminEmailRecipients" 
                  value={settings.notifications.adminEmailRecipients} 
                  onChange={(e) => handleInputChange('notifications', 'adminEmailRecipients', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">Separate multiple email addresses with commas</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
};

export default AdminSettings;
