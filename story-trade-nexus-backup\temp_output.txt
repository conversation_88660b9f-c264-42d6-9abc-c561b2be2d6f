﻿import { useState, useEffect, useRef } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import {
  Book as BookIcon,
  MapPin,
  Star,
  Calendar,
  ArrowLeft,
  MessageCircle,
  Heart,
  Share2,
  User,
  ChevronLeft,
  ChevronRight,
  Tag,
  Clock,
  Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { getBook } from '@/lib/bookService';
import { Book } from '@/types';
import { useAuth } from '@/lib/AuthContext';
import MainLayout from '@/components/layouts/MainLayout';
import { GeoCoordinates, getCurrentPosition, calculateDistance, reverseGeocode, getBasicLocationInfo } from '@/lib/geolocationUtils';

const BookDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [book, setBook] = useState<Book | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  // Location related states
  const [userLocation, setUserLocation] = useState<GeoCoordinates | null>(null);
  const [userLocationInfo, setUserLocationInfo] = useState<{
    city?: string;
    state?: string;
    pincode?: string;
    fullAddress?: string;
  } | null>(null);
  const [ownerLocationInfo, setOwnerLocationInfo] = useState<{
    city?: string;
    state?: string;
    pincode?: string;
    fullAddress?: string;
  } | null>(null);
  const [distance, setDistance] = useState<number | null>(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [isLoadingOwnerLocation, setIsLoadingOwnerLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [ownerLocationError, setOwnerLocationError] = useState<string | null>(null);
  const [locationPermission, setLocationPermission] = useState<'granted' | 'denied' | 'unknown'>('unknown');
  const watchPositionId = useRef<number | null>(null);

  // Function to get user's current location
  const getUserLocation = async () => {
    setIsLoadingLocation(true);
    setLocationError(null);

    try {
      const position = await getCurrentPosition();
      setUserLocation(position);
      setLocationPermission('granted');

      // Get location information using reverse geocoding
      try {
        console.log('Attempting to reverse geocode coordinates:', position);
        const locationInfo = await reverseGeocode(position);
        console.log('Reverse geocoding successful, received data:', locationInfo);

        const userInfo = {
          city: locationInfo.city,
          state: locationInfo.state,
          pincode: locationInfo.pincode,
          fullAddress: locationInfo.fullAddress
        };

        console.log('Setting user location info:', userInfo);
        setUserLocationInfo(userInfo);

        // Show a toast notification for debugging
        toast.success('Location information updated');
      } catch (geocodeErr) {
        console.error('Error reverse geocoding:', geocodeErr);

        // Use fallback location information
        console.log('Using fallback location information');
        const basicInfo = getBasicLocationInfo(position);
        setUserLocationInfo({
          fullAddress: basicInfo.fullAddress
        });

        toast.error('Using basic location information (coordinates only)');
      }

      return position;
    } catch (err) {
      console.error('Error getting user location:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error getting location';
      setLocationError(errorMessage);

      if (errorMessage.includes('denied')) {
        setLocationPermission('denied');
      }

      return null;
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // Function to get owner's location information from coordinates
  const getOwnerLocationInfo = async (coordinates: GeoCoordinates) => {
    setIsLoadingOwnerLocation(true);
    setOwnerLocationError(null);

    try {
      console.log('Getting owner location info for coordinates:', coordinates);
      const locationInfo = await reverseGeocode(coordinates);
      console.log('Owner location info received:', locationInfo);

      setOwnerLocationInfo({
        city: locationInfo.city,
        state: locationInfo.state,
        pincode: locationInfo.pincode,
        fullAddress: locationInfo.fullAddress
      });

      return locationInfo;
    } catch (error) {
      console.error('Error getting owner location info:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error getting location';
      setOwnerLocationError(errorMessage);

      // Use fallback location information
      const basicInfo = getBasicLocationInfo(coordinates);
      setOwnerLocationInfo({
        fullAddress: basicInfo.fullAddress
      });

      return null;
    } finally {
      setIsLoadingOwnerLocation(false);
    }
  };

  // Function to calculate distance between user and book owner
  const calculateDistanceToOwner = (userCoords: GeoCoordinates, ownerCoords: GeoCoordinates) => {
    const calculatedDistance = calculateDistance(userCoords, ownerCoords);
    // Set distance with one decimal place precision
    setDistance(parseFloat(calculatedDistance.toFixed(1)));
    return calculatedDistance;
  };

  // Function to start watching user's position for real-time updates
  const startWatchingPosition = () => {
    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by your browser');
      return;
    }

    // Clear any existing watch
    if (watchPositionId.current !== null) {
      navigator.geolocation.clearWatch(watchPositionId.current);
    }

    watchPositionId.current = navigator.geolocation.watchPosition(
      async (position) => {
        const newUserLocation = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        };

        setUserLocation(newUserLocation);
        setLocationPermission('granted');

        // If book has owner coordinates, recalculate distance
        if (book?.ownerCoordinates) {
          calculateDistanceToOwner(newUserLocation, book.ownerCoordinates);
        }

        // Update location information using reverse geocoding
        try {
          console.log('Watch position: Attempting to reverse geocode coordinates:', newUserLocation);
          const locationInfo = await reverseGeocode(newUserLocation);
          console.log('Watch position: Reverse geocoding successful, received data:', locationInfo);

          const userInfo = {
            city: locationInfo.city,
            state: locationInfo.state,
            pincode: locationInfo.pincode,
            fullAddress: locationInfo.fullAddress
          };

          console.log('Watch position: Setting user location info:', userInfo);
          setUserLocationInfo(userInfo);
        } catch (geocodeErr) {
          console.error('Error reverse geocoding in watch position:', geocodeErr);

          // Use fallback location information
          console.log('Watch position: Using fallback location information');
          const basicInfo = getBasicLocationInfo(newUserLocation);
          setUserLocationInfo({
            fullAddress: basicInfo.fullAddress
          });
        }
      },
      (error) => {
        console.error('Error watching position:', error);
        let errorMessage = 'Unknown error occurred while tracking location';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'User denied the request for geolocation';
            setLocationPermission('denied');
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'The request to get user location timed out';
            break;
        }

        setLocationError(errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  };

  // Stop watching position when component unmounts
  useEffect(() => {
    return () => {
      if (watchPositionId.current !== null) {
        navigator.geolocation.clearWatch(watchPositionId.current);
      }
    };
  }, []);

  useEffect(() => {
    const fetchBook = async () => {
      if (!id) {
        setError('Book ID is missing');
        setLoading(false);
        return;
      }

      try {
        const bookData = await getBook(id);

        if (bookData) {
          setBook(bookData);

          // Set the current image index to the display image index if available
          if (bookData.displayImageIndex !== undefined &&
              bookData.imageUrls &&
              bookData.displayImageIndex < bookData.imageUrls.length) {
            setCurrentImageIndex(bookData.displayImageIndex);
          }

          // Always try to get user location first, regardless of book owner coordinates
          console.log('Automatically requesting user location...');
          const userCoords = await getUserLocation();

          // If book has owner coordinates, get owner location info and calculate distance
          if (bookData.ownerCoordinates) {
            console.log('Book has owner coordinates:', bookData.ownerCoordinates);
            // Get owner location information
            await getOwnerLocationInfo(bookData.ownerCoordinates);

            // Calculate distance if we have user coordinates
            if (userCoords) {
              calculateDistanceToOwner(userCoords, bookData.ownerCoordinates);
              // Start watching position for real-time updates
              startWatchingPosition();
            }
          } else {
            console.log('Book does not have owner coordinates');
          }
        } else {
          setError('Book not found');
        }
      } catch (err) {
        console.error('Error fetching book:', err);
        setError('Failed to load book details');
      } finally {
        setLoading(false);
      }
    };

    fetchBook();

    // Cleanup function
    return () => {
      if (watchPositionId.current !== null) {
        navigator.geolocation.clearWatch(watchPositionId.current);
        watchPositionId.current = null;
      }
    };
  }, [id]);

  // Format date to readable format
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  // Navigate to previous image
  const prevImage = () => {
    if (book?.imageUrls && book.imageUrls.length > 1) {
      setCurrentImageIndex((prev) =>
        prev === 0 ? book.imageUrls!.length - 1 : prev - 1
      );
    }
  };

  // Navigate to next image
  const nextImage = () => {
    if (book?.imageUrls && book.imageUrls.length > 1) {
      setCurrentImageIndex((prev) =>
        prev === book.imageUrls!.length - 1 ? 0 : prev + 1
      );
    }
  };

  // Handle adding to wishlist
  const handleAddToWishlist = () => {
    if (!currentUser) {
      toast.error('Please sign in to add books to your wishlist');
      navigate('/signin');
      return;
    }

    toast.success('Book added to your wishlist');
    // Implement actual wishlist functionality here
  };

  // Handle contacting the owner
  const handleContactOwner = () => {
    if (!currentUser) {
      toast.error('Please sign in to contact book owners');
      navigate('/signin');
      return;
    }

    toast.success('Message feature coming soon!');
    // Implement actual messaging functionality here
  };

  // Handle requesting location permission
  const handleRequestLocation = async () => {
    console.log('handleRequestLocation: Current permission status:', locationPermission);

    // Clear any previous location errors
    setLocationError(null);

    if (locationPermission === 'denied') {
      // If permission was denied, provide detailed instructions based on browser/device
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      let message = 'Location permission was denied. ';

      if (isMobile) {
        message += 'Please go to your device settings, find this app/website, and enable location access.';
      } else {
        // Desktop browsers
        const browser = detectBrowser();
        switch (browser) {
          case 'chrome':
            message += 'Click the lock icon in the address bar, select "Site settings", and allow location access.';
            break;
          case 'firefox':
            message += 'Click the lock icon in the address bar, select "Clear Permission", then try again.';
            break;
          case 'safari':
            message += 'Go to Safari Preferences > Websites > Location, and allow access for this website.';
            break;
          default:
            message += 'Please enable location access in your browser settings and refresh the page.';
        }
      }

      toast.error(message, { duration: 6000 });
      return;
    }

    toast.loading('Updating your location...', { id: 'location-toast' });
    console.log('handleRequestLocation: Calling getUserLocation()');

    const userCoords = await getUserLocation();

    if (userCoords) {
      console.log('handleRequestLocation: Got user coordinates:', userCoords);

      if (book?.ownerCoordinates) {
        console.log('handleRequestLocation: Calculating distance to owner');
        calculateDistanceToOwner(userCoords, book.ownerCoordinates);
        console.log('handleRequestLocation: Starting position watching');
        startWatchingPosition();
        toast.success('Location updated. Distance calculation refreshed.', { id: 'location-toast' });
      } else {
        console.log('handleRequestLocation: No owner coordinates available');
        toast.success('Location updated, but owner coordinates not available.', { id: 'location-toast' });
      }
    } else {
      console.log('handleRequestLocation: Failed to get user coordinates');
      toast.error('Could not get your location. Please check your device settings and try again.', { id: 'location-toast' });
    }
  };

  // Helper function to detect browser type
  const detectBrowser = (): string => {
    const userAgent = navigator.userAgent.toLowerCase();

    if (userAgent.indexOf('chrome') > -1) return 'chrome';
    if (userAgent.indexOf('firefox') > -1) return 'firefox';
    if (userAgent.indexOf('safari') > -1) return 'safari';
    if (userAgent.indexOf('edge') > -1) return 'edge';

    return 'unknown';
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-6">
              <Link to="/browse" className="text-burgundy-500 hover:text-burgundy-600 flex items-center">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Browse
              </Link>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <Skeleton className="h-[400px] w-full rounded-lg" />
              </div>
              <div>
                <Skeleton className="h-10 w-3/4 mb-2" />
                <Skeleton className="h-6 w-1/2 mb-4" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4 mb-6" />
                <Skeleton className="h-20 w-full mb-6" />
                <div className="flex gap-2">
                  <Skeleton className="h-10 w-24" />
                  <Skeleton className="h-10 w-24" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !book) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <h1 className="text-2xl font-bold text-navy-800 mb-4">
              {error || 'Book not found'}
            </h1>
            <p className="text-gray-600 mb-6">
              We couldn't find the book you're looking for.
            </p>
            <Link to="/browse">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Browse
              </Button>
            </Link>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center mb-6">
            <Link to="/browse" className="text-burgundy-500 hover:text-burgundy-600 flex items-center">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Browse
            </Link>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Book Image Section */}
            <div>
              <div className="relative rounded-lg overflow-hidden shadow-md mb-4 flex justify-center">
                {/* Use carousel if multiple images are available */}
                {book.imageUrls && book.imageUrls.length > 1 ? (
                  <div className="max-h-[400px] max-w-[300px] relative">
                    {/* Image carousel */}
                    <div className="relative">
                      <div className="overflow-hidden">
                        <img
                          src={book.imageUrls[currentImageIndex]}
                          alt={`${book.title} - Image ${currentImageIndex + 1}`}
                          className="w-full h-auto object-contain max-h-[400px]"
                        />
                      </div>

                      {/* Navigation buttons */}
                      <button
                        onClick={prevImage}
                        className="absolute left-0 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-1 rounded-r-md"
                      >
                        <ChevronLeft className="h-6 w-6" />
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-0 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-1 rounded-l-md"
                      >
                        <ChevronRight className="h-6 w-6" />
                      </button>

                      {/* Image counter */}
                      <div className="absolute bottom-2 left-1/2 -translate-x-1/2 bg-black/50 text-white px-2 py-1 rounded-md text-sm">
                        {currentImageIndex + 1} / {book.imageUrls.length}
                      </div>
                    </div>
                  </div>
                ) : (
                  // Single image display
                  <div className="max-h-[400px] max-w-[300px]">
                    <img
                      src={book.imageUrl}
                      alt={book.title}
                      className="w-full h-auto object-contain"
                    />
                  </div>
                )}
              </div>

              {/* Owner Information Card */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-navy-800 mb-3">Owner Information</h3>
                <div className="flex items-center mb-3">
                  <User className="h-4 w-4 mr-2 text-gray-500" />
                  <span className="font-medium">{book.ownerName}</span>
                </div>

                {/* Distance as clickable Google Maps link */}
                {(distance !== null || book.distance) && book.ownerCoordinates && (
                  <div className="flex items-center mb-3">
                    <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                    <a
                      href={`https://www.google.com/maps?q=${book.ownerCoordinates.latitude},${book.ownerCoordinates.longitude}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-burgundy-600 hover:underline"
                    >
                      {distance !== null
                        ? `${distance.toFixed(1)} km away from you`
                        : `${typeof book.distance === 'number'
                            ? book.distance.toFixed(1)
                            : book.distance} km away from you`}
                    </a>
                    {userLocation && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 ml-1 text-gray-500 hover:text-burgundy-600"
                        onClick={handleRequestLocation}
                        title="Refresh distance calculation"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
                        </svg>
                      </Button>
                    )}
                  </div>
                )}

                <div className="flex items-center mb-3">
                  <Star className="h-4 w-4 mr-2 text-yellow-500" />
                  <span className="font-medium">{book.ownerRating}/5 Rating</span>
                </div>
                <Button
                  onClick={handleContactOwner}
                  className="w-full mt-2"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Contact Owner
                </Button>
              </div>
            </div>

            {/* Book Details Section */}
            <div>
              <h1 className="text-3xl font-bold text-navy-800 mb-2">{book.title}</h1>
              <p className="text-xl text-gray-700 mb-4">by {book.author}</p>

              <div className="flex flex-wrap gap-2 mb-4">
                {book.genre.map((genre, index) => (
                  <Badge key={index} variant="outline" className="bg-gray-100">
                    {genre}
                  </Badge>
                ))}
              </div>

              <div className="flex items-center gap-4 mb-4 text-sm">
                <div className="flex items-center">
                  <BookIcon className="h-4 w-4 mr-1 text-gray-500" />
                  <span>Condition: <strong>{book.condition}</strong></span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                  <span>Listed: <strong>{formatDate(book.createdAt)}</strong></span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="font-medium text-navy-800 mb-2">Description</h3>
                <p className="text-gray-700">{book.description}</p>
              </div>

              <div className="flex flex-wrap gap-4 mb-6">
                {book.price && (
                  <div className="bg-green-50 border border-green-200 rounded-md px-4 py-2">
                    <div className="text-sm text-gray-600 flex items-center">
                      <Tag className="h-3 w-3 mr-1" />
                      Sale Price
                    </div>
                    <div className="text-xl font-semibold text-green-600">â‚¹{book.price}</div>
                  </div>
                )}

                {book.rentalPrice && (
                  <div className="bg-blue-50 border border-blue-200 rounded-md px-4 py-2">
                    <div className="text-sm text-gray-600 flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      Rental Price
                    </div>
                    <div className="text-xl font-semibold text-blue-600">
                      â‚¹{book.rentalPrice} <span className="text-sm font-normal">{book.rentalPeriod}</span>
                    </div>
                  </div>
                )}

                <div className="bg-purple-50 border border-purple-200 rounded-md px-4 py-2">
                  <div className="text-sm text-gray-600 flex items-center">
                    <Info className="h-3 w-3 mr-1" />
                    Perceived Value
                  </div>
                  <div className="text-xl font-semibold text-purple-600">{book.perceivedValue}/10</div>
                </div>
              </div>

              {book.isbn && (
                <div className="mb-6">
                  <h3 className="font-medium text-navy-800 mb-2">Additional Information</h3>
                  <p className="text-gray-700"><strong>ISBN:</strong> {book.isbn}</p>
                </div>
              )}

              <div className="flex flex-wrap gap-3">
                <Button onClick={handleAddToWishlist} className="flex items-center">
                  <Heart className="h-4 w-4 mr-2" />
                  Add to Wishlist
                </Button>

                <Button variant="outline" onClick={() => {
                  navigator.share({
                    title: book.title,
                    text: `Check out ${book.title} by ${book.author} on BookSwap`,
                    url: window.location.href
                  }).catch(err => {
                    console.error('Error sharing:', err);
                    toast.error('Sharing failed. Try copying the URL manually.');
                  });
                }} className="flex items-center">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default BookDetail;
