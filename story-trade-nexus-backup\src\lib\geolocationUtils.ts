/**
 * Interface for geolocation coordinates
 */
export interface GeoCoordinates {
  latitude: number;
  longitude: number;
}

/**
 * Interface for location data
 */
export interface LocationData {
  state?: string;
  city?: string;
  pincode?: string;
  fullAddress?: string;
  coordinates?: GeoCoordinates; // GPS coordinates
}

/**
 * Get the current geolocation coordinates from the device
 * @param options Optional parameters for geolocation request
 * @returns Promise that resolves to the coordinates or rejects with an error
 */
export const getCurrentPosition = (options?: {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
}): Promise<GeoCoordinates> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by your browser'));
      return;
    }

    // Default options with longer timeout
    const defaultOptions = {
      enableHighAccuracy: true,
      timeout: 20000, // Increased from 10000 to 20000ms
      maximumAge: 0
    };

    // Merge provided options with defaults
    const geoOptions = { ...defaultOptions, ...options };

    console.log('Getting position with options:', geoOptions);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log('Position obtained successfully');
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        });
      },
      (error) => {
        let errorMessage = 'Unknown error occurred while getting location';
        let errorCode = 'UNKNOWN';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location permission denied. Please enable location services in your browser settings.';
            errorCode = 'PERMISSION_DENIED';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable. Please try again in a different area.';
            errorCode = 'POSITION_UNAVAILABLE';
            break;
          case error.TIMEOUT:
            errorMessage = 'The request to get your location timed out. Please check your internet connection and try again.';
            errorCode = 'TIMEOUT';
            break;
        }

        console.error(`Geolocation error (${errorCode}):`, errorMessage);

        // Create an error object with additional properties
        const enhancedError = new Error(errorMessage);
        (enhancedError as any).code = errorCode;
        (enhancedError as any).originalError = error;

        reject(enhancedError);
      },
      geoOptions
    );
  });
};

/**
 * Reverse geocode coordinates to get address information
 * @param coordinates The latitude and longitude
 * @returns Promise that resolves to location data
 */
export const reverseGeocode = async (
  coordinates: GeoCoordinates
): Promise<LocationData> => {
  try {
    console.log('reverseGeocode: Starting reverse geocoding for coordinates:', coordinates);

    // Using OpenStreetMap Nominatim API for reverse geocoding
    // Note: For production, consider using a commercial API with proper rate limiting
    const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${coordinates.latitude}&lon=${coordinates.longitude}&addressdetails=1`;
    console.log('reverseGeocode: Fetching from URL:', url);

    const response = await fetch(url, {
      headers: {
        'Accept-Language': 'en-US,en;q=0.9',
        'User-Agent': 'BookSwap Application (https://bookswap.example.com)'
      }
    });

    console.log('reverseGeocode: Response status:', response.status);

    if (!response.ok) {
      console.error('reverseGeocode: Response not OK:', response.status, response.statusText);
      throw new Error(`Failed to fetch location data: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('reverseGeocode: Received data:', data);

    // Extract relevant address components
    const address = data.address || {};
    console.log('reverseGeocode: Extracted address:', address);

    const locationData = {
      state: address.state || '',
      city: address.city || address.town || address.village || '',
      pincode: address.postcode || '',
      fullAddress: data.display_name || ''
    };

    console.log('reverseGeocode: Returning location data:', locationData);
    return locationData;
  } catch (error) {
    console.error('Error in reverse geocoding:', error);
    throw error;
  }
};

/**
 * Get location data from device GPS with retry and fallback mechanisms
 * @returns Promise that resolves to location data including coordinates
 */
export const getLocationFromGPS = async (): Promise<LocationData> => {
  try {
    // First try with high accuracy
    console.log('Attempting to get location with high accuracy...');
    try {
      const coordinates = await getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 15000
      });
      console.log('Successfully got high accuracy location');
      const locationData = await reverseGeocode(coordinates);
      return {
        ...locationData,
        coordinates: coordinates
      };
    } catch (error) {
      // If high accuracy fails with timeout, try with lower accuracy
      if ((error as any)?.code === 'TIMEOUT') {
        console.log('High accuracy location timed out, trying with lower accuracy...');
        const coordinates = await getCurrentPosition({
          enableHighAccuracy: false,
          timeout: 20000,
          maximumAge: 60000 // Accept a cached position up to 1 minute old
        });
        console.log('Successfully got location with lower accuracy');
        const locationData = await reverseGeocode(coordinates);
        return {
          ...locationData,
          coordinates: coordinates
        };
      } else {
        // For other errors, just rethrow
        throw error;
      }
    }
  } catch (error) {
    console.error('Error getting location from GPS:', error);

    // If we have a permission denied error, provide a clear message
    if ((error as any)?.code === 'PERMISSION_DENIED') {
      throw new Error('Location access was denied. Please enable location services in your browser or device settings to use this feature.');
    }

    throw error;
  }
};

/**
 * Calculate distance between two coordinates in kilometers
 * @param coords1 First coordinates
 * @param coords2 Second coordinates
 * @returns Distance in kilometers
 */

/**
 * Fallback function to get location information from coordinates
 * This is used when the reverse geocoding API fails
 * @param coordinates The latitude and longitude
 * @returns LocationData with basic information
 */
export const getBasicLocationInfo = (coordinates: GeoCoordinates): LocationData => {
  // Format coordinates to 6 decimal places
  const lat = coordinates.latitude.toFixed(6);
  const lng = coordinates.longitude.toFixed(6);

  // Create a simple location description
  return {
    fullAddress: `Coordinates: ${lat}, ${lng}`,
    coordinates: coordinates
  };
};

export const calculateDistance = (
  coords1: GeoCoordinates,
  coords2: GeoCoordinates
): number => {
  // Haversine formula to calculate distance between two points on Earth
  const R = 6371; // Radius of the Earth in km
  const dLat = (coords2.latitude - coords1.latitude) * Math.PI / 180;
  const dLon = (coords2.longitude - coords1.longitude) * Math.PI / 180;

  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(coords1.latitude * Math.PI / 180) * Math.cos(coords2.latitude * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km

  return Math.round(distance * 10) / 10; // Round to 1 decimal place
};

/**
 * Get user's current location silently without showing any UI
 * @returns Promise that resolves to the user's coordinates or null if permission denied
 */
export const getUserLocationSilently = async (): Promise<GeoCoordinates | null> => {
  try {
    console.log('Getting user location silently...');

    // Try with high accuracy first
    try {
      const coordinates = await getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 10000
      });
      console.log('Got high-accuracy user location:', coordinates);
      return coordinates;
    } catch (error) {
      // If high accuracy fails, try with lower accuracy
      console.log('High accuracy location failed, trying with lower accuracy...');
      if ((error as any)?.code !== 'PERMISSION_DENIED') {
        const coordinates = await getCurrentPosition({
          enableHighAccuracy: false,
          timeout: 15000,
          maximumAge: 60000 // Accept a cached position up to 1 minute old
        });
        console.log('Got low-accuracy user location:', coordinates);
        return coordinates;
      }

      // If permission denied, return null
      console.log('Location permission denied');
      return null;
    }
  } catch (error) {
    console.error('Error getting user location silently:', error);
    return null;
  }
};
