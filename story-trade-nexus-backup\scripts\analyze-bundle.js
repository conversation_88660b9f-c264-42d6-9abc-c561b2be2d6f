#!/usr/bin/env node

/**
 * Bundle Analysis Script
 *
 * Analyzes the Vite build output to provide insights into bundle size and optimization
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DIST_DIR = path.join(__dirname, '../dist');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeBundle() {
  console.log('🔍 Analyzing PeerBooks Bundle...\n');

  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ Build directory not found. Please run "npm run build" first.');
    process.exit(1);
  }

  if (!fs.existsSync(ASSETS_DIR)) {
    console.error('❌ Assets directory not found in build output.');
    process.exit(1);
  }

  const files = fs.readdirSync(ASSETS_DIR);
  const jsFiles = files.filter(file => file.endsWith('.js'));
  const cssFiles = files.filter(file => file.endsWith('.css'));
  const otherFiles = files.filter(file => !file.endsWith('.js') && !file.endsWith('.css'));

  let totalSize = 0;
  let jsSize = 0;
  let cssSize = 0;
  let otherSize = 0;

  const fileAnalysis = [];

  // Analyze JavaScript files
  console.log('📦 JavaScript Bundles:');
  console.log('─'.repeat(80));
  jsFiles.forEach(file => {
    const filePath = path.join(ASSETS_DIR, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    jsSize += size;
    totalSize += size;

    // Determine chunk type based on filename
    let chunkType = 'Unknown';
    if (file.includes('index-')) chunkType = 'Main Entry';
    else if (file.includes('vendor-')) chunkType = 'Vendor';
    else if (file.includes('admin-')) chunkType = 'Admin';
    else if (file.includes('user-')) chunkType = 'User';
    else if (file.includes('auth-')) chunkType = 'Auth';
    else if (file.includes('book-')) chunkType = 'Books';
    else if (file.includes('info-')) chunkType = 'Info Pages';
    else if (file.includes('legal-')) chunkType = 'Legal Pages';
    else if (file.includes('dev-')) chunkType = 'Development';
    else if (file.includes('OwnerInformation-')) chunkType = 'Owner Info (Lazy)';

    fileAnalysis.push({
      name: file,
      size,
      type: 'JS',
      chunkType
    });

    console.log(`  ${file.padEnd(50)} ${formatBytes(size).padStart(10)} (${chunkType})`);
  });

  console.log('─'.repeat(80));
  console.log(`  Total JavaScript: ${formatBytes(jsSize)}\n`);

  // Analyze CSS files
  if (cssFiles.length > 0) {
    console.log('🎨 CSS Files:');
    console.log('─'.repeat(80));
    cssFiles.forEach(file => {
      const filePath = path.join(ASSETS_DIR, file);
      const stats = fs.statSync(filePath);
      const size = stats.size;
      cssSize += size;
      totalSize += size;

      fileAnalysis.push({
        name: file,
        size,
        type: 'CSS',
        chunkType: 'Styles'
      });

      console.log(`  ${file.padEnd(50)} ${formatBytes(size).padStart(10)}`);
    });
    console.log('─'.repeat(80));
    console.log(`  Total CSS: ${formatBytes(cssSize)}\n`);
  }

  // Analyze other files
  if (otherFiles.length > 0) {
    console.log('📄 Other Assets:');
    console.log('─'.repeat(80));
    otherFiles.forEach(file => {
      const filePath = path.join(ASSETS_DIR, file);
      const stats = fs.statSync(filePath);
      const size = stats.size;
      otherSize += size;
      totalSize += size;

      console.log(`  ${file.padEnd(50)} ${formatBytes(size).padStart(10)}`);
    });
    console.log('─'.repeat(80));
    console.log(`  Total Other Assets: ${formatBytes(otherSize)}\n`);
  }

  // Summary
  console.log('📊 Bundle Summary:');
  console.log('═'.repeat(80));
  console.log(`  JavaScript:     ${formatBytes(jsSize).padStart(10)} (${((jsSize / totalSize) * 100).toFixed(1)}%)`);
  console.log(`  CSS:            ${formatBytes(cssSize).padStart(10)} (${((cssSize / totalSize) * 100).toFixed(1)}%)`);
  console.log(`  Other Assets:   ${formatBytes(otherSize).padStart(10)} (${((otherSize / totalSize) * 100).toFixed(1)}%)`);
  console.log('─'.repeat(80));
  console.log(`  Total Bundle:   ${formatBytes(totalSize).padStart(10)}`);
  console.log('═'.repeat(80));

  // Performance recommendations
  console.log('\n💡 Performance Recommendations:');
  console.log('─'.repeat(80));

  const largeJsFiles = fileAnalysis.filter(f => f.type === 'JS' && f.size > 100 * 1024);
  if (largeJsFiles.length > 0) {
    console.log('⚠️  Large JavaScript chunks detected:');
    largeJsFiles.forEach(file => {
      console.log(`   • ${file.name} (${formatBytes(file.size)}) - Consider further splitting`);
    });
  }

  if (jsSize > 500 * 1024) {
    console.log('⚠️  Total JavaScript size is large. Consider:');
    console.log('   • More aggressive code splitting');
    console.log('   • Tree shaking optimization');
    console.log('   • Removing unused dependencies');
  }

  const mainChunk = fileAnalysis.find(f => f.chunkType === 'Main Entry');
  if (mainChunk && mainChunk.size > 200 * 1024) {
    console.log('⚠️  Main entry chunk is large. Consider:');
    console.log('   • Moving more components to lazy loading');
    console.log('   • Splitting vendor dependencies');
  }

  console.log('\n✅ Optimization Status:');
  console.log('─'.repeat(80));
  console.log(`  Code Splitting:     ${jsFiles.length > 5 ? '✅ Active' : '⚠️  Limited'}`);
  console.log(`  Lazy Loading:       ${fileAnalysis.some(f => f.name.includes('OwnerInformation')) ? '✅ Implemented' : '❌ Missing'}`);
  console.log(`  Vendor Splitting:   ${fileAnalysis.some(f => f.name.includes('vendor-')) ? '✅ Active' : '❌ Missing'}`);
  console.log(`  Admin Separation:   ${fileAnalysis.some(f => f.name.includes('admin-')) ? '✅ Active' : '❌ Missing'}`);

  console.log('\n🎯 Bundle Analysis Complete!\n');
}

// Run if this is the main module
analyzeBundle();

export { analyzeBundle };
