import React, { useState } from 'react';
import { Database, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import { seedRealBooksToFirebase } from '@/lib/bookService';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import AdminLayout from '@/components/layout/AdminLayout';

const AdminUtilities: React.FC = () => {
  const [isSeedingBooks, setIsSeedingBooks] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [currentAction, setCurrentAction] = useState<'seed' | 'purge' | null>(null);

  const openConfirmDialog = (action: 'seed' | 'purge') => {
    setCurrentAction(action);
    setIsConfirmDialogOpen(true);
  };

  const handleSeedBooks = async () => {
    try {
      setIsSeedingBooks(true);
      await seedRealBooksToFirebase();
      toast.success('Sample books added to the database successfully!');
      setIsConfirmDialogOpen(false);
    } catch (error) {
      console.error('Error seeding books:', error);
      toast.error('Failed to seed books. Please try again.');
    } finally {
      setIsSeedingBooks(false);
    }
  };

  // This is a placeholder function - in a real app, you'd implement this
  const handlePurgeData = async () => {
    try {
      // Implement data purging logic here
      toast.success('Data purged successfully!');
      setIsConfirmDialogOpen(false);
    } catch (error) {
      console.error('Error purging data:', error);
      toast.error('Failed to purge data. Please try again.');
    }
  };

  const handleConfirmAction = () => {
    if (currentAction === 'seed') {
      handleSeedBooks();
    } else if (currentAction === 'purge') {
      handlePurgeData();
    }
  };

  const utilityItems = [
    {
      title: 'Seed Sample Books',
      description: 'Add sample books to the database for testing',
      icon: <Database className="h-8 w-8 text-burgundy-500" />,
      action: () => openConfirmDialog('seed'),
      color: 'bg-burgundy-50'
    },
    {
      title: 'Purge Test Data',
      description: 'Remove test data from the database',
      icon: <Trash2 className="h-8 w-8 text-red-500" />,
      action: () => openConfirmDialog('purge'),
      color: 'bg-red-50'
    }
  ];

  return (
    <AdminLayout title="Admin Tools" description="Access administrative utilities and functions">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-navy-800 mb-2">Admin Tools</h1>
          <p className="text-gray-600">Access admin tools and utilities</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {utilityItems.map((item, index) => (
          <div key={index} className={`rounded-lg shadow-md p-6 transition-all hover:shadow-lg ${item.color}`}>
            <div className="flex flex-col items-center text-center">
              {item.icon}
              <h2 className="text-xl font-semibold mt-4 mb-2">{item.title}</h2>
              <p className="text-gray-600 mb-4">{item.description}</p>
              <Button variant="outline" className="w-full" onClick={item.action}>
                Run Utility
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {currentAction === 'seed' ? 'Seed Sample Books' : 'Purge Test Data'}
            </DialogTitle>
            <DialogDescription>
              {currentAction === 'seed'
                ? 'Are you sure you want to add sample books to the database? This will create duplicate books if they already exist.'
                : 'Are you sure you want to purge test data from the database? This action cannot be undone.'}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsConfirmDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant={currentAction === 'purge' ? 'destructive' : 'default'}
              onClick={handleConfirmAction}
              disabled={isSeedingBooks}
            >
              {isSeedingBooks ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Processing...
                </>
              ) : (
                'Confirm'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminUtilities;
