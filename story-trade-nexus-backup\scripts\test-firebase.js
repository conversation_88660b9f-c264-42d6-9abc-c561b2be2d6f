#!/usr/bin/env node

/**
 * Firebase Connection Test Script
 * 
 * This script tests the Firebase Admin SDK configuration and connection
 * to ensure everything is set up correctly before running the WebP migration.
 */

import { initializeFirebaseAdmin, testFirebaseConnection } from './firebase-config.js';

async function main() {
  console.log('🔥 Testing Firebase Admin SDK Configuration...\n');

  try {
    // Initialize Firebase Admin SDK
    console.log('1. Initializing Firebase Admin SDK...');
    const services = await initializeFirebaseAdmin();
    console.log('✅ Firebase Admin SDK initialized successfully');
    console.log(`   Project ID: ${services.adminApp.options.projectId}`);
    console.log(`   Storage Bucket: ${services.storage.bucket().name}\n`);

    // Test connections
    console.log('2. Testing Firebase connections...');
    const connectionOk = await testFirebaseConnection();
    
    if (connectionOk) {
      console.log('✅ All Firebase connections successful!\n');
      
      // Test basic operations
      console.log('3. Testing basic operations...');
      
      // Test Storage listing
      try {
        const bucket = services.storage.bucket();
        const [files] = await bucket.getFiles({
          prefix: 'book-images/',
          maxResults: 5
        });
        console.log(`✅ Storage access successful - Found ${files.length} sample files`);
      } catch (error) {
        console.log(`⚠️  Storage listing failed: ${error.message}`);
      }
      
      // Test Firestore query
      try {
        const booksCollection = services.firestore.collection('books');
        const snapshot = await booksCollection.limit(1).get();
        console.log(`✅ Firestore access successful - Found ${snapshot.size} sample documents`);
      } catch (error) {
        console.log(`⚠️  Firestore query failed: ${error.message}`);
      }
      
      console.log('\n🎉 Firebase configuration test completed successfully!');
      console.log('You can now run the WebP migration script with confidence.');
      
    } else {
      console.log('❌ Firebase connection test failed');
      console.log('Please check your configuration and try again.');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Firebase configuration test failed:', error.message);
    console.error('\nPlease check the following:');
    console.error('1. Firebase service account credentials are properly configured');
    console.error('2. The service account has the necessary permissions');
    console.error('3. The project ID and storage bucket are correct');
    console.error('4. Your internet connection is stable');
    console.error('\nSee the README.md file for detailed setup instructions.');
    process.exit(1);
  }
}

// Run the test
main();
