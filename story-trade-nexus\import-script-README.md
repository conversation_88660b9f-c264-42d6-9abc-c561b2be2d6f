# Hyderabad Properties Import Script

This script imports data from the "hyderabad_properties - google.csv" file into a Firebase Firestore collection named "hyderabadProperties".

## Prerequisites

- Node.js (v14 or higher)
- npm (Node Package Manager)
- Firebase project with Firestore enabled

## Setup

1. Make sure the CSV file "hyderabad_properties - google.csv" is in the same directory as the script.

2. Install the required dependencies:
   ```bash
   # Install dependencies from the import-script-package.json file
   npm install firebase csv-parse
   ```

   Or if you prefer to use the provided package.json:
   ```bash
   # Create a new directory for the import script (optional)
   mkdir hyderabad-properties-import
   cd hyderabad-properties-import

   # Copy the script files and CSV to this directory
   cp ../importHyderabadProperties.js .
   cp ../hyderabad_properties\ -\ google.csv .
   cp ../import-script-package.json ./package.json

   # Install dependencies
   npm install
   ```

## Running the Script

Execute the script with:

```bash
node importHyderabadProperties.js
```

You can also specify a custom batch size (default is 500):

```bash
node importHyderabadProperties.js --batch-size=100
```

A smaller batch size may be useful if you encounter memory issues or timeouts with large datasets.

You should see output similar to:

```
[INFO] 2025-05-23T18:15:26.789Z: Starting import process...
[INFO] 2025-05-23T18:15:26.791Z: Reading CSV file: C:\path\to\hyderabad_properties - google.csv
[SUCCESS] 2025-05-23T18:15:28.123Z: Batch of 500 records imported successfully. Total: 500
[SUCCESS] 2025-05-23T18:15:29.456Z: Batch of 500 records imported successfully. Total: 1000
[SUCCESS] 2025-05-23T18:15:30.789Z: Batch of 408 records imported successfully. Total: 1408

[INFO] 2025-05-23T18:15:30.790Z: ===== Import Summary =====
[SUCCESS] 2025-05-23T18:15:30.790Z: Total records processed: 1408
[SUCCESS] 2025-05-23T18:15:30.790Z: Total records imported: 1408
[SUCCESS] 2025-05-23T18:15:30.790Z: No errors encountered during import
[INFO] 2025-05-23T18:15:30.790Z: Import process completed!
```

## What the Script Does

1. Reads the CSV file "hyderabad_properties - google.csv"
2. Transforms each row into a Firestore document format
3. Imports the data in batches to the "hyderabadProperties" collection in Firestore
4. Converts latitude and longitude to numbers
5. Converts pincode to a number if possible
6. Adds a timestamp for when the record was imported
7. Provides progress updates and error reporting

## Output

The script will output:
- Progress updates for each batch of records imported
- A summary of the total records imported and any errors encountered
- Detailed error messages for any issues during the import process

## Data Structure

Each document in the Firestore collection will have the following structure:

```javascript
{
  communityName: String,
  address: String,
  pincode: Number or String,
  location: {
    latitude: Number,
    longitude: Number
  },
  createdAt: Timestamp
}
```

## Error Handling

The script includes error handling for:
- CSV parsing errors
- Data transformation errors
- Firestore batch write errors

Any errors will be logged to the console with details about the specific record or batch that caused the error.
