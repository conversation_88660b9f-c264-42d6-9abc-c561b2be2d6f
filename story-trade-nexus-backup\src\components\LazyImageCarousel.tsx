/**
 * LazyImageCarousel Component
 * 
 * Lazy-loaded wrapper for Embla Carousel to reduce initial bundle size
 */

import React, { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Carousel loading skeleton
const CarouselSkeleton: React.FC<{ height?: string }> = ({ height = "400px" }) => (
  <div className="relative w-full bg-gray-100 rounded-lg overflow-hidden" style={{ height }}>
    <Skeleton className="w-full h-full" />
    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
      {[...Array(3)].map((_, i) => (
        <Skeleton key={i} className="w-2 h-2 rounded-full" />
      ))}
    </div>
  </div>
);

// Lazy load embla carousel
const useEmblaCarousel = React.lazy(() => 
  import('embla-carousel-react').then(module => ({ default: module.default }))
);

// Lazy load the actual ImageCarousel component
const ImageCarousel = React.lazy(() => import('./ImageCarousel'));

// Props interface for the lazy carousel
interface LazyImageCarouselProps {
  images: string[];
  initialIndex?: number;
  alt: string;
  containerHeight?: string;
  maxZoomLevel?: number;
  className?: string;
  fallback?: React.ReactNode;
}

const LazyImageCarousel: React.FC<LazyImageCarouselProps> = ({
  images,
  initialIndex = 0,
  alt,
  containerHeight = "400px",
  maxZoomLevel = 2,
  className,
  fallback
}) => {
  return (
    <Suspense fallback={fallback || <CarouselSkeleton height={containerHeight} />}>
      <ImageCarousel
        images={images}
        initialIndex={initialIndex}
        alt={alt}
        containerHeight={containerHeight}
        maxZoomLevel={maxZoomLevel}
        className={className}
      />
    </Suspense>
  );
};

export { useEmblaCarousel, CarouselSkeleton };
export default LazyImageCarousel;
