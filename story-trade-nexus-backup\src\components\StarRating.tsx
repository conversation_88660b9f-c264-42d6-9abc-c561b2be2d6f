/**
 * StarRating Component
 * 
 * Interactive star rating component for user feedback
 */

import React, { useState } from 'react';
import { Star } from 'lucide-react';

interface StarRatingProps {
  /** Current rating value (1-5) */
  value?: number;
  /** Callback when rating changes */
  onChange?: (rating: number) => void;
  /** Whether the rating is read-only */
  readonly?: boolean;
  /** Size of the stars */
  size?: 'sm' | 'md' | 'lg';
  /** Whether to show the rating text */
  showText?: boolean;
  /** Custom class name */
  className?: string;
  /** Whether the component is disabled */
  disabled?: boolean;
}

const StarRating: React.FC<StarRatingProps> = ({
  value = 0,
  onChange,
  readonly = false,
  size = 'md',
  showText = true,
  className = '',
  disabled = false
}) => {
  const [hoverRating, setHoverRating] = useState(0);
  const [isHovering, setIsHovering] = useState(false);

  // Size configurations
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  // Rating descriptions
  const ratingTexts = {
    0: 'No rating',
    1: 'Poor',
    2: 'Fair',
    3: 'Good',
    4: 'Very Good',
    5: 'Excellent'
  };

  const handleStarClick = (rating: number) => {
    if (readonly || disabled) return;
    onChange?.(rating);
  };

  const handleStarHover = (rating: number) => {
    if (readonly || disabled) return;
    setHoverRating(rating);
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    if (readonly || disabled) return;
    setIsHovering(false);
    setHoverRating(0);
  };

  const displayRating = isHovering ? hoverRating : value;
  const displayText = isHovering ? ratingTexts[hoverRating as keyof typeof ratingTexts] : ratingTexts[value as keyof typeof ratingTexts];

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Stars */}
      <div 
        className="flex items-center gap-1"
        onMouseLeave={handleMouseLeave}
      >
        {[1, 2, 3, 4, 5].map((star) => {
          const isFilled = star <= displayRating;
          const isInteractive = !readonly && !disabled;
          
          return (
            <button
              key={star}
              type="button"
              className={`
                ${sizeClasses[size]}
                ${isInteractive ? 'cursor-pointer hover:scale-110 transition-transform' : 'cursor-default'}
                ${disabled ? 'opacity-50' : ''}
                focus:outline-none focus:ring-2 focus:ring-burgundy-500 focus:ring-offset-1 rounded
              `}
              onClick={() => handleStarClick(star)}
              onMouseEnter={() => handleStarHover(star)}
              disabled={disabled || readonly}
              aria-label={`Rate ${star} star${star !== 1 ? 's' : ''}`}
            >
              <Star
                className={`
                  ${sizeClasses[size]}
                  transition-colors duration-150
                  ${isFilled 
                    ? 'fill-yellow-400 text-yellow-400' 
                    : 'fill-none text-gray-300 hover:text-yellow-400'
                  }
                `}
              />
            </button>
          );
        })}
      </div>

      {/* Rating text */}
      {showText && (
        <div className="flex items-center gap-2">
          <span className={`${textSizeClasses[size]} text-gray-600 font-medium`}>
            {displayRating > 0 ? `${displayRating}/5` : ''}
          </span>
          {displayRating > 0 && (
            <span className={`${textSizeClasses[size]} text-gray-500`}>
              ({displayText})
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default StarRating;
