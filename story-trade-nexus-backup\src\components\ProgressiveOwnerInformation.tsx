/**
 * ProgressiveOwnerInformation Component
 * 
 * Progressive loading wrapper for Owner Information
 * Shows data as it becomes available to improve perceived performance
 * Optimized for LCP and Core Web Vitals
 */

import React, { useState, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Book } from '@/types';
import { GeoCoordinates } from '@/lib/geolocationUtils';
import {
  MapPin,
  Star,
  User,
  MessageCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { BookStatusBadge } from '@/components/BookStatusBadge';

interface ProgressiveOwnerInformationProps {
  book: Book;
  distance: number | null;
  userLocation: GeoCoordinates | null;
  ownerPincode: string | null;
  locationPermission: 'granted' | 'denied' | 'unknown';
  onContactOwner: () => void;
  onRequestLocation: () => void;
  /** Loading states for different data pieces */
  isLoadingDistance?: boolean;
  isLoadingLocation?: boolean;
  isLoadingOwnerData?: boolean;
}

const ProgressiveOwnerInformation: React.FC<ProgressiveOwnerInformationProps> = ({
  book,
  distance,
  userLocation,
  ownerPincode,
  locationPermission,
  onContactOwner,
  onRequestLocation,
  isLoadingDistance = false,
  isLoadingLocation = false,
  isLoadingOwnerData = false
}) => {
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    // Show content immediately for LCP optimization
    setShowContent(true);
  }, []);

  if (!showContent) {
    return (
      <div className="w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm">
        <Skeleton className="h-6 w-40 mb-4 animate-pulse" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Skeleton className="h-16 w-full animate-pulse" />
          <Skeleton className="h-16 w-full animate-pulse" />
          <Skeleton className="h-16 w-full animate-pulse" />
          <Skeleton className="h-16 w-full animate-pulse" />
        </div>
        <Skeleton className="w-full h-12 mt-5 animate-pulse" />
      </div>
    );
  }

  return (
    <div className="w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm">
      <h3 className="font-medium text-navy-800 mb-4 text-lg">Owner Information</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Owner name card - Always available from book data */}
        <div className="flex flex-col p-3 bg-gray-50 rounded-md transition-opacity duration-300">
          <div className="flex items-center">
            <User className="h-5 w-5 mr-3 text-navy-400" />
            {isLoadingOwnerData ? (
              <Skeleton className="h-5 w-24 animate-pulse" />
            ) : (
              <span className="font-medium">{book.ownerName}</span>
            )}
          </div>
        </div>

        {/* Location Information - Progressive loading */}
        <div className="flex items-center p-3 bg-gray-50 rounded-md transition-opacity duration-300">
          <MapPin className="h-5 w-5 mr-3 text-burgundy-400" />
          <div className="flex-1">
            {/* GPS coordinates available - show distance as clickable Google Maps link */}
            {book.ownerCoordinates && (
              <>
                {isLoadingDistance ? (
                  <Skeleton className="h-4 w-32 mb-1 animate-pulse" />
                ) : (
                  <a
                    href={`https://www.google.com/maps?q=${book.ownerCoordinates.latitude},${book.ownerCoordinates.longitude}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-burgundy-600 hover:underline font-medium block"
                  >
                    {distance !== null
                      ? `${distance.toFixed(1)} km away from you`
                      : book.distance
                        ? `${typeof book.distance === 'number'
                            ? book.distance.toFixed(1)
                            : book.distance} km away from you`
                        : `View on map`}
                  </a>
                )}
                {book.ownerCommunity && (
                  <span className="text-sm text-gray-600 block">
                    {book.ownerCommunity}
                  </span>
                )}
              </>
            )}

            {/* No GPS coordinates - show pincode or location */}
            {!book.ownerCoordinates && (
              <>
                {isLoadingLocation ? (
                  <Skeleton className="h-4 w-28 animate-pulse" />
                ) : (
                  <span className="text-gray-700">
                    {ownerPincode || book.ownerPincode || book.ownerLocation || 'Location not available'}
                  </span>
                )}
                {book.ownerCommunity && (
                  <span className="text-sm text-gray-600 block">
                    {book.ownerCommunity}
                  </span>
                )}
              </>
            )}
          </div>
        </div>

        {/* Rating card - Always available from book data */}
        <div className="flex items-center p-3 bg-gray-50 rounded-md transition-opacity duration-300">
          <Star className="h-5 w-5 mr-3 text-yellow-400" />
          {isLoadingOwnerData ? (
            <Skeleton className="h-5 w-20 animate-pulse" />
          ) : (
            <div className="flex items-center">
              <span className="font-medium mr-1">{book.ownerRating.toFixed(1)}</span>
              <span className="text-gray-600">/ 5.0</span>
            </div>
          )}
        </div>

        {/* Status card - Always available from book data */}
        <div className="p-3 bg-gray-50 rounded-md transition-opacity duration-300">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Status</span>
            <BookStatusBadge
              status={book.status}
              nextAvailableDate={book.nextAvailableDate}
              className="text-xs px-2 py-1"
            />
          </div>
        </div>

        {/* Security deposit info - if available */}
        {book.securityDepositRequired && book.securityDepositAmount && (
          <div className="flex items-center p-3 bg-gray-50 rounded-md transition-opacity duration-300">
            <MessageCircle className="h-5 w-5 mr-3 text-blue-400" />
            <div>
              <span className="text-sm text-gray-600">Security Deposit</span>
              <div className="font-medium">₹{book.securityDepositAmount}</div>
            </div>
          </div>
        )}

        {/* Additional info - if available */}
        {book.ownerEmail && (
          <div className="flex items-center p-3 bg-gray-50 rounded-md transition-opacity duration-300">
            <MessageCircle className="h-5 w-5 mr-3 text-green-400" />
            <div className="flex-1">
              <span className="text-sm text-gray-600">Contact</span>
              <div className="text-sm font-medium">Available via app</div>
            </div>
          </div>
        )}
      </div>

      {/* Contact Owner Button */}
      <div className="mt-5">
        <Button
          onClick={onContactOwner}
          className="w-full bg-burgundy-600 hover:bg-burgundy-700 text-white font-medium py-3 px-4 rounded-md transition-colors duration-200"
        >
          <MessageCircle className="h-5 w-5 mr-2" />
          Contact Owner
        </Button>
      </div>

      {/* Location permission request - if needed */}
      {locationPermission === 'denied' && !userLocation && (
        <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-800 mb-2">
            Enable location access to see accurate distances to books near you.
          </p>
          <Button
            onClick={onRequestLocation}
            variant="outline"
            size="sm"
            className="text-yellow-800 border-yellow-300 hover:bg-yellow-100"
          >
            Enable Location
          </Button>
        </div>
      )}
    </div>
  );
};

export default ProgressiveOwnerInformation;
