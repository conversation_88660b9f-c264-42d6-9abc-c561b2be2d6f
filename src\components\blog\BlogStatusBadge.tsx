import React from 'react';

interface BlogStatusBadgeProps {
  published: boolean;
  draft: boolean;
}

const BlogStatusBadge: React.FC<BlogStatusBadgeProps> = ({ published, draft }) => {
  if (draft) {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        Draft
      </span>
    );
  }

  return (
    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
      Published
    </span>
  );
};

export default BlogStatusBadge;
