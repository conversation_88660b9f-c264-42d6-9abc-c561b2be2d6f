/**
 * Service for looking up location information from Indian pincodes
 */

import { LocationData } from './geolocationUtils';

/**
 * Interface for the response from the Postal PIN Code API
 */
interface PincodeApiResponse {
  Message: string;
  Status: 'Success' | 'Error';
  PostOffice: PostOfficeData[] | null;
}

/**
 * Interface for post office data returned by the API
 */
interface PostOfficeData {
  Name: string;
  Description: string;
  BranchType: string;
  DeliveryStatus: string;
  Circle: string;
  District: string;
  Division: string;
  Region: string;
  State: string;
  Country: string;
  Pincode?: string;
  PINCode?: string; // API sometimes uses this capitalization
}

/**
 * Lookup location information from a pincode
 * @param pincode - The Indian postal pincode to lookup
 * @returns Promise<LocationData> - Location information including city, state, etc.
 */
export const lookupPincodeLocation = async (pincode: string): Promise<LocationData | null> => {
  try {
    console.log(`Looking up location for pincode: ${pincode}`);
    
    // Validate pincode format (Indian pincodes are 6 digits)
    if (!/^\d{6}$/.test(pincode)) {
      console.error(`Invalid pincode format: ${pincode}`);
      return null;
    }
    
    // Call the Postal PIN Code API
    const response = await fetch(`https://api.postalpincode.in/pincode/${pincode}`, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'PeerBooks Application (https://peerbooks.example.com)'
      }
    });
    
    if (!response.ok) {
      console.error(`API request failed with status: ${response.status}`);
      return null;
    }
    
    const data = await response.json() as PincodeApiResponse[];
    
    // API returns an array with a single response object
    if (!data || !data[0] || data[0].Status !== 'Success' || !data[0].PostOffice || data[0].PostOffice.length === 0) {
      console.error(`No data found for pincode: ${pincode}`);
      return null;
    }
    
    // Get the first post office data (usually the main one)
    const postOffice = data[0].PostOffice[0];
    
    // Create location data from the post office information
    const locationData: LocationData = {
      city: postOffice.District || '',
      state: postOffice.State || '',
      pincode: pincode,
      fullAddress: `${postOffice.Name}, ${postOffice.District}, ${postOffice.State}, ${pincode}, ${postOffice.Country}`
    };
    
    console.log(`Location data for pincode ${pincode}:`, locationData);
    return locationData;
  } catch (error) {
    console.error(`Error looking up pincode ${pincode}:`, error);
    return null;
  }
};

/**
 * Add a delay between API requests to avoid rate limiting
 * @param ms - Milliseconds to delay
 */
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Batch process pincodes with a delay between requests
 * @param pincodes - Array of pincodes to process
 * @param delayMs - Milliseconds to delay between requests
 * @returns Promise<Map<string, LocationData>> - Map of pincodes to location data
 */
export const batchProcessPincodes = async (
  pincodes: string[],
  delayMs = 1000
): Promise<Map<string, LocationData>> => {
  const results = new Map<string, LocationData>();
  
  for (const pincode of pincodes) {
    // Skip if already processed
    if (results.has(pincode)) continue;
    
    const locationData = await lookupPincodeLocation(pincode);
    
    if (locationData) {
      results.set(pincode, locationData);
    }
    
    // Add delay between requests to avoid rate limiting
    if (delayMs > 0 && pincodes.indexOf(pincode) < pincodes.length - 1) {
      await delay(delayMs);
    }
  }
  
  return results;
};
