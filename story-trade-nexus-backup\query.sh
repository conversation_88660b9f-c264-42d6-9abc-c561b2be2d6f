#!/bin/bash

# Firebase Community Query Tool - Unix Shell Script
# Usage: ./query.sh <pincode>
# Example: ./query.sh 500001

# Check if pincode argument is provided
if [ $# -eq 0 ]; then
    echo ""
    echo "🏘️  Firebase Community Query Tool"
    echo "📍 Searching Hyderabad Properties Database"
    echo ""
    echo "❌ Error: Pincode argument is required"
    echo ""
    echo "📋 Usage: ./query.sh <pincode>"
    echo "📝 Example: ./query.sh 500001"
    echo ""
    exit 1
fi

echo ""
echo "🚀 Starting Firebase Community Query..."
echo ""

# Run the Node.js script with the provided pincode
node queryCommunities.js "$1"

# Check the exit status
if [ $? -ne 0 ]; then
    echo ""
    echo "❌ Query failed with error code $?"
    echo ""
    exit $?
fi

echo ""
echo "✅ Query completed successfully!"
echo ""
