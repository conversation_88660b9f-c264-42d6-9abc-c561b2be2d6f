# Firebase Admin SDK Configuration
# 
# Choose one of the following authentication methods:

# Method 1: Service Account Key File (Recommended)
# Download the service account JSON file from Firebase Console and set the path here
# GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# Method 2: Environment Variables
# Extract these values from your service account JSON file
# FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
# FIREBASE_CLIENT_EMAIL="<EMAIL>"
# FIREBASE_PRIVATE_KEY_ID="your-private-key-id"
# FIREBASE_CLIENT_ID="your-client-id"

# Migration Configuration (Optional)
# Uncomment and modify these values to customize the migration behavior

# Maximum image width in pixels
# WEBP_MAX_WIDTH=1200

# WebP quality (0-100)
# WEBP_QUALITY=85

# Batch size for processing images
# WEBP_BATCH_SIZE=10

# Delay between batches in milliseconds
# WEBP_BATCH_DELAY=1000

# Enable/disable backups (true/false)
# WEBP_CREATE_BACKUPS=true

# Log level (debug, info, warn, error)
# WEBP_LOG_LEVEL=info
