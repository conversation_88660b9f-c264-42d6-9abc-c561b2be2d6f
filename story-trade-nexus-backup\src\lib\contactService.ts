import { db } from './firebase';
import { getUserDocument } from './userService';
import { sendEmailVerification } from 'firebase/auth';
import { analytics } from './firebase';
import { logEvent } from 'firebase/analytics';

/**
 * Interface for contact data
 */
export interface ContactData {
  ownerPhone?: string;
  ownerName: string;
  ownerEmail?: string;
  success: boolean;
  message: string;
}

/**
 * Get owner's contact information from Firestore
 * @param ownerId The ID of the book owner
 * @returns Promise that resolves to the owner's contact information
 */
export const getOwnerContactInfo = async (ownerId: string): Promise<ContactData> => {
  try {
    console.log(`Getting contact info for owner ID: ${ownerId}`);
    
    // Get user document from Firestore
    const userData = await getUserDocument(ownerId);
    
    if (!userData) {
      console.error(`No user document found for owner ID: ${ownerId}`);
      return {
        success: false,
        message: "Owner information not found",
        ownerName: "Unknown"
      };
    }
    
    console.log(`Found owner data:`, userData);
    
    return {
      ownerPhone: userData.phone || undefined,
      ownerName: userData.displayName || "Unknown",
      ownerEmail: userData.email || undefined,
      success: true,
      message: "Owner contact information retrieved successfully"
    };
  } catch (error) {
    console.error(`Error getting owner contact info:`, error);
    return {
      success: false,
      message: "Failed to retrieve owner contact information",
      ownerName: "Unknown"
    };
  }
};

/**
 * Launch WhatsApp with a pre-filled message
 * @param phoneNumber The phone number to send the message to
 * @param message The pre-filled message
 * @returns Boolean indicating success
 */
export const launchWhatsApp = (phoneNumber: string, message: string): boolean => {
  try {
    // Format phone number (remove spaces, dashes, etc.)
    const formattedPhone = phoneNumber.replace(/[\s-\(\)]/g, '');
    
    // Encode the message for URL
    const encodedMessage = encodeURIComponent(message);
    
    // Create WhatsApp URL
    const whatsappUrl = `https://wa.me/${formattedPhone}?text=${encodedMessage}`;
    
    // Open WhatsApp in a new window
    window.open(whatsappUrl, '_blank');
    
    // Track the event
    if (analytics) {
      logEvent(analytics, 'contact_whatsapp_launched', {
        phone_number: formattedPhone
      });
    }
    
    return true;
  } catch (error) {
    console.error('Error launching WhatsApp:', error);
    return false;
  }
};

/**
 * Send an email notification to the book owner
 * @param ownerEmail The email address of the book owner
 * @param bookTitle The title of the book
 * @param interestedUserName The name of the interested user
 * @param interestedUserEmail The email of the interested user
 * @returns Promise that resolves to a boolean indicating success
 */
export const sendOwnerEmailNotification = async (
  ownerEmail: string,
  bookTitle: string,
  interestedUserName: string,
  interestedUserEmail: string
): Promise<boolean> => {
  try {
    // In a real implementation, this would call a server-side API to send the email
    // For now, we'll just log it and return success
    console.log(`Would send email to ${ownerEmail} about interest in book "${bookTitle}" from ${interestedUserName} (${interestedUserEmail})`);
    
    // Track the event
    if (analytics) {
      logEvent(analytics, 'contact_email_notification_sent', {
        book_title: bookTitle
      });
    }
    
    return true;
  } catch (error) {
    console.error('Error sending email notification:', error);
    return false;
  }
};

/**
 * Track contact interaction for analytics
 * @param bookId The ID of the book
 * @param ownerId The ID of the book owner
 * @param userId The ID of the interested user
 * @param contactMethod The method used to contact the owner (whatsapp, email, etc.)
 */
export const trackContactInteraction = (
  bookId: string,
  ownerId: string,
  userId: string,
  contactMethod: 'whatsapp' | 'email' | 'fallback'
): void => {
  if (analytics) {
    logEvent(analytics, 'book_owner_contacted', {
      book_id: bookId,
      owner_id: ownerId,
      user_id: userId,
      contact_method: contactMethod,
      timestamp: new Date().toISOString()
    });
  }
};
