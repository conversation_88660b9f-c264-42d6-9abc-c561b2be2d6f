import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { seedRealBooksToFirebase } from '@/lib/bookService';
import { toast } from 'sonner';

const SeedBooks: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSeedBooks = async () => {
    if (!confirm('Are you sure you want to seed the database with real books? This will add duplicate books if they already exist.')) {
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      await seedRealBooksToFirebase();
      setSuccess(true);
      toast.success('Real books added to the database successfully!');
    } catch (error) {
      console.error('Error seeding books:', error);
      setError('Failed to seed books. See console for details.');
      toast.error('Failed to seed books');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8">
            <h1 className="text-3xl font-playfair font-bold text-navy-800 mb-4">Seed Database</h1>
            <p className="text-gray-600 mb-6">
              This utility page allows you to seed the database with real books for testing purposes.
              Use this only in development environments.
            </p>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
                <p>{error}</p>
              </div>
            )}

            {success && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
                <p>Real books added to the database successfully!</p>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={handleSeedBooks}
                disabled={loading}
                className="w-full sm:w-auto"
              >
                {loading ? 'Adding Real Books...' : 'Add Real Books'}
              </Button>

              <Link to="/browse">
                <Button
                  variant="outline"
                  className="w-full sm:w-auto"
                >
                  Browse Books
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default SeedBooks;
